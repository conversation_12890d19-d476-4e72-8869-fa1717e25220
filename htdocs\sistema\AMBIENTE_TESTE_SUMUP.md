# 🧪 Ambiente de Teste SumUp

## Problema Atual
Você está recebendo o erro: `"param":"pay_to_email or merchant_code"` - isso significa que a API precisa de um desses parâmetros válidos.

## ✅ Soluções para Ambiente de Teste

### Opção 1: Usar Email da Conta SumUp (Recomendado)

1. **Configure o email** no `config.php`:
```php
$SUMUP_PAY_TO_EMAIL = "<EMAIL>"; // Email da sua conta SumUp
$SUMUP_MERCHANT_CODE = ""; // Deixe vazio se usar email
```

2. **Use o email da conta** que você usa para fazer login na SumUp

### Opção 2: Verificar Merchant Code

O merchant code "53902197" pode não estar correto para sua conta. Vamos verificar:

1. **Execute o teste** em: `http://seudominio.com/sistema/test_sumup_integration.php`
2. **Na seção "2. Teste de Conectividade"**, procure por "Merchant Profile"
3. **Compare** o merchant_code retornado com o configurado

### Opção 3: Obter Merchant Code Correto

1. **Acesse**: https://me.sumup.com/
2. **Vá em**: Configurações → Perfil da Empresa
3. **Copie o Merchant Code** correto
4. **Atualize** no `config.php`:
```php
$SUMUP_MERCHANT_CODE = "SEU_CODIGO_CORRETO";
```

## 🔧 Configuração Recomendada para Teste

```php
// CONFIGURAÇÕES DA API SUMUP
$SUMUP_API_KEY = "sup_sk_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"; // Sua chave privada
$SUMUP_CLIENT_ID = ""; // Deixe vazio se usar chave privada
$SUMUP_CLIENT_SECRET = ""; // Deixe vazio se usar chave privada
$SUMUP_MERCHANT_CODE = ""; // Deixe vazio se usar email
$SUMUP_PAY_TO_EMAIL = "<EMAIL>"; // Email da conta SumUp
$SUMUP_SANDBOX = true; // SEMPRE true para testes
$SUMUP_API_URL = "https://api.sumup.com";
$SUMUP_WEBHOOK_SECRET = "";
```

## 🎯 Passos para Resolver

### Passo 1: Atualizar Configuração
Edite `htdocs/sistema/config.php` e adicione seu email:
```php
$SUMUP_PAY_TO_EMAIL = "<EMAIL>";
```

### Passo 2: Testar Novamente
1. Acesse: `http://seudominio.com/sistema/test_sumup_integration.php`
2. Clique em "Criar Checkout de Teste (Direto)"
3. Verifique se o erro desapareceu

### Passo 3: Verificar Merchant Profile
Na seção "2. Teste de Conectividade", procure por:
- ✅ Merchant Profile OK
- Informações sobre seu merchant_code correto

## ❓ Perguntas Frequentes

**P: Preciso de uma conta especial para testes?**
R: Não, use sua conta SumUp normal com `$SUMUP_SANDBOX = true`

**P: O email deve ser o mesmo do login?**
R: Sim, use o email que você usa para fazer login na SumUp

**P: Posso usar merchant_code E pay_to_email?**
R: Não, use apenas UM dos dois. Recomendamos `pay_to_email` para testes

**P: Como sei se minha conta está ativa?**
R: Se você consegue fazer login em https://me.sumup.com/, está ativa

## 🔍 Debug

Para ver exatamente o que está sendo enviado:
1. Abra o console do navegador (F12)
2. Execute o teste
3. Verifique os logs no console

## 📞 Próximos Passos

1. **Configure o email** no config.php
2. **Teste novamente** 
3. **Se ainda der erro**, me informe:
   - O email que você está usando
   - A resposta completa do teste de conectividade
   - O merchant_code retornado pela API (se houver)

---

**Importante**: Em ambiente de teste, sempre use `$SUMUP_SANDBOX = true`!
