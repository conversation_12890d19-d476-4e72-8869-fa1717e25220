# 🔑 Como Obter Credenciais SumUp

## Problema Atual
Você está usando uma chave pública (`sup_pk_`) que não permite autenticação na API. Você precisa de uma chave privada (`sup_sk_`) ou configurar OAuth.

## ✅ Solução 1: Chave <PERSON> (Recomendado)

### Passo 1: Acessar o Dashboard SumUp
1. Acesse: https://me.sumup.com/
2. Faça login com sua conta SumUp
3. Vá em **Configurações** → **Desenvolvedor** → **API Keys**

### Passo 2: Gerar Chave Privada
1. Clique em **"Gerar Nova Chave"**
2. Selecione **"Server-side"** (chave privada)
3. Copie a chave que começa com `sup_sk_`

### Passo 3: Configurar no Sistema
No arquivo `config.php`, substitua:
```php
$SUMUP_API_KEY = "sup_pk_1NkvQAtqEJM5LHouJh7L8gZ0NYXKcMK08"; // ❌ Chave pública
```

Por:
```php
$SUMUP_API_KEY = "sup_sk_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"; // ✅ Chave privada
```

## 🔄 Solução 2: OAuth (Alternativa)

Se você não conseguir obter uma chave privada, use OAuth:

### Passo 1: Criar Aplicação
1. Acesse: https://developer.sumup.com/
2. Clique em **"Create Application"**
3. Preencha os dados da aplicação

### Passo 2: Obter Credenciais OAuth
1. Copie o **Client ID**
2. Copie o **Client Secret**
3. Configure no `config.php`:

```php
$SUMUP_API_KEY = ""; // Deixe vazio para OAuth
$SUMUP_CLIENT_ID = "seu_client_id_aqui";
$SUMUP_CLIENT_SECRET = "seu_client_secret_aqui";
```

## 🧪 Ambiente de Teste

### Sandbox (Recomendado para Testes)
```php
$SUMUP_SANDBOX = true;
```

### Produção (Apenas após testes)
```php
$SUMUP_SANDBOX = false;
```

## 📋 Configuração Completa

Exemplo de configuração funcional:

```php
// CONFIGURAÇÕES DA API SUMUP
$SUMUP_API_KEY = "sup_sk_1234567890abcdefghijklmnopqrstuv"; // Chave privada
$SUMUP_CLIENT_ID = ""; // Deixe vazio se usar chave privada
$SUMUP_CLIENT_SECRET = ""; // Deixe vazio se usar chave privada
$SUMUP_MERCHANT_CODE = "53902197"; // Seu código de merchant
$SUMUP_SANDBOX = true; // true para testes, false para produção
$SUMUP_API_URL = $SUMUP_SANDBOX ? "https://api.sumup.com" : "https://api.sumup.com";
$SUMUP_WEBHOOK_SECRET = ""; // Opcional
```

## 🔍 Verificar Configuração

Após configurar, teste novamente:
1. Acesse: `http://seudominio.com/sistema/test_sumup_integration.php`
2. Verifique se o teste de conectividade passa

## ❓ Dúvidas Frequentes

**P: Onde encontro meu Merchant Code?**
R: No dashboard SumUp, vá em Configurações → Perfil da Empresa

**P: Posso usar a mesma chave para sandbox e produção?**
R: Não, você precisa de chaves diferentes para cada ambiente

**P: Como sei se minha chave está correta?**
R: Chaves privadas começam com `sup_sk_`, chaves públicas com `sup_pk_`

## 🆘 Suporte

Se ainda tiver problemas:
1. Verifique se sua conta SumUp está ativa
2. Confirme se você tem permissões de desenvolvedor
3. Entre em contato com o suporte SumUp: https://sumup.com/support

---

**Importante**: Nunca compartilhe suas chaves privadas ou credenciais OAuth!
