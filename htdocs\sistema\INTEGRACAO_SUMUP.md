# Integração SumUp - Sistema PDV

Este documento descreve como configurar e usar a integração com a SumUp no seu sistema PDV.

## 📋 Pré-requisitos

1. **Conta SumUp**: Você precisa ter uma conta SumUp ativa
2. **Credenciais da API**: Obtenha suas credenciais no [SumUp Developer Portal](https://developer.sumup.com/)
3. **PHP cURL**: Certifique-se de que a extensão cURL está habilitada no PHP

## 🔧 Configuração

### 1. Configurar Credenciais

Edite o arquivo `config.php` e preencha as seguintes variáveis:

```php
// CONFIGURAÇÕES DA API SUMUP
$SUMUP_API_KEY = "sup_sk_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"; // Chave PRIVADA (sup_sk_)
$SUMUP_CLIENT_ID = ""; // Deixe vazio se usar chave privada
$SUMUP_CLIENT_SECRET = ""; // Deixe vazio se usar chave privada
$SUMUP_MERCHANT_CODE = "seu_merchant_code_aqui"; // Código do merchant SumUp
$SUMUP_SANDBOX = true; // true para sandbox, false para produção
$SUMUP_WEBHOOK_SECRET = "seu_webhook_secret"; // Secret para validar webhooks (opcional)
```

**⚠️ IMPORTANTE**: Use uma chave PRIVADA (`sup_sk_`), não pública (`sup_pk_`).
Veja `CREDENCIAIS_SUMUP.md` para instruções detalhadas.

### 2. Adicionar Método de Pagamento

Execute o script SQL para adicionar SumUp como método de pagamento:

```sql
-- Execute este comando no seu banco de dados
source htdocs/sistema/install/add_sumup_payment_method.sql
```

Ou execute manualmente:

```sql
INSERT INTO `tec_meiopagamento` (`cod`, `nome`, `status`, `dias`, `id`) VALUES
('sumup', 'SumUp', 1, '', 10)
ON DUPLICATE KEY UPDATE 
`nome` = 'SumUp', 
`status` = 1;
```

### 3. Configurar Webhooks (Opcional)

Para receber notificações automáticas de pagamentos, configure o webhook no painel da SumUp:

- **URL do Webhook**: `https://seudominio.com/sistema/sumup_webhook`
- **Eventos**: Selecione `CHECKOUT_PAID` e `CHECKOUT_FAILED`

## 🚀 Como Usar

### No PDV

1. **Adicionar Produtos**: Adicione produtos ao carrinho normalmente
2. **Selecionar SumUp**: No modal de pagamento, selecione "SumUp" como método de pagamento
3. **Informar Valor**: Digite o valor a ser pago
4. **Processar Pagamento**: O sistema abrirá uma nova janela com o checkout da SumUp
5. **Finalizar**: Após o pagamento, a janela fechará automaticamente e o pagamento será registrado

### Fluxo de Pagamento

1. Sistema cria um checkout na SumUp
2. Cliente é redirecionado para página de pagamento da SumUp
3. Cliente insere dados do cartão e confirma pagamento
4. SumUp processa o pagamento
5. Sistema recebe confirmação e registra o pagamento
6. Venda é finalizada automaticamente

## 🔍 Testes

### Ambiente Sandbox

Para testar a integração:

1. Configure `$SUMUP_SANDBOX = true` no `config.php`
2. Use credenciais de sandbox da SumUp
3. Acesse: `https://seudominio.com/sistema/pos`
4. Faça uma venda teste usando SumUp

### Cartões de Teste

No ambiente sandbox, use estes cartões de teste:

- **Aprovado**: 4000 0000 0000 0002
- **Recusado**: 4000 0000 0000 0069
- **CVV**: Qualquer 3 dígitos
- **Validade**: Qualquer data futura

### Testar Webhooks

Acesse: `https://seudominio.com/sistema/sumup_webhook/test` (apenas em desenvolvimento)

## 📊 Monitoramento

### Logs

Os logs da integração são salvos em:
- `htdocs/sistema/app/logs/` (logs do CodeIgniter)

### Verificar Status

Para verificar se a integração está funcionando:

```php
// No controlador ou modelo
$this->load->model('sumup_model');
$test = $this->sumup_model->testConnection();
if ($test['success']) {
    echo "Conexão OK";
} else {
    echo "Erro: " . $test['message'];
}
```

## 🛠️ Personalização

### Modificar Interface

Para personalizar a interface de pagamento SumUp:

1. Edite: `htdocs/sistema/themes/default/views/pos/index.php`
2. Modifique a função `processSumUpPayment()` no JavaScript

### Adicionar Campos

Para capturar informações adicionais do cliente:

1. Modifique o método `createCheckout()` no `Sumup_model.php`
2. Adicione campos no formulário de pagamento

### Webhooks Personalizados

Para processar eventos específicos:

1. Edite: `htdocs/sistema/app/controllers/Sumup_webhook.php`
2. Adicione novos casos no switch do `event_type`

## ❗ Troubleshooting

### Problemas Comuns

**Erro: "SumUp não está configurado"**
- Verifique se as credenciais estão corretas no `config.php`
- Certifique-se de que o `SUMUP_API_KEY` e `SUMUP_MERCHANT_CODE` estão preenchidos

**Erro: "cURL Error"**
- Verifique se a extensão cURL está habilitada no PHP
- Verifique conectividade com a internet

**Pagamento não é registrado**
- Verifique se o webhook está configurado corretamente
- Verifique os logs em `app/logs/`

**Checkout não abre**
- Verifique se o popup não está sendo bloqueado pelo navegador
- Teste em modo incógnito

### Debug

Para ativar debug detalhado, adicione no `config.php`:

```php
// Ativar logs detalhados
log_message('debug', 'SumUp debug ativo');
```

## 📞 Suporte

- **Documentação SumUp**: https://developer.sumup.com/api
- **Suporte SumUp**: https://sumup.com/support
- **Logs do Sistema**: `htdocs/sistema/app/logs/`

## 🔄 Atualizações

Para atualizar a integração:

1. Faça backup dos arquivos modificados
2. Substitua os arquivos da integração
3. Execute novos scripts SQL se necessário
4. Teste em ambiente de desenvolvimento primeiro

---

**Versão**: 1.0  
**Data**: <?php echo date('d/m/Y'); ?>  
**Compatibilidade**: PHP 7.0+, CodeIgniter 3.x
