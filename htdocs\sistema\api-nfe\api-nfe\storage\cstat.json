[{"cod": "100", "msg": "Autorizado o uso da NF-e", "status": "1", "correct": ""}, {"cod": "101", "msg": "Cancelamento de NF-e homologado", "status": "1", "correct": ""}, {"cod": "102", "msg": "Inutilização de número homologado", "status": "1", "correct": ""}, {"cod": "103", "msg": "Lote recebido com sucesso", "status": "1", "correct": ""}, {"cod": "104", "msg": "Lote processado", "status": "1", "correct": ""}, {"cod": "105", "msg": "Lote em processamento", "status": "1", "correct": ""}, {"cod": "106", "msg": "Lote não localizado", "status": "0", "correct": "O numero de recibo não foi localizado, confira"}, {"cod": "107", "msg": "Serviço em Operação", "status": "1", "correct": ""}, {"cod": "108", "msg": "Serviço Paralisado <PERSON> (curto prazo)", "status": "0", "correct": "Fora do ar AGUARDE!"}, {"cod": "109", "msg": "Serviço Paralisado sem Previsão", "status": "0", "correct": "Fora do ar tente usar CONTINGÊNCIA"}, {"cod": "110", "msg": "<PERSON><PERSON>", "status": "0", "correct": "Cuidado algo errado com o registro da empresa na SEFAZ"}, {"cod": "111", "msg": "Consulta cadastro com uma ocorrência", "status": "1", "correct": ""}, {"cod": "112", "msg": "Consulta cadastro com mais de uma ocorrência", "status": "1", "correct": ""}, {"cod": "113", "msg": "SVC em processo de desativação SVC será  desabilitada para a SEFAZ-XX em dd/mm/aa às hh:mm horas", "status": "0", "correct": "Contingência indisponível"}, {"cod": "114", "msg": "SVC-RS desabilitada pela SEFAZ de Origem", "status": "0", "correct": "Contingência indisponível"}, {"cod": "124", "msg": "EPEC Autorizado", "status": "1", "correct": ""}, {"cod": "128", "msg": "Lote de Evento Processado", "status": "1", "correct": ""}, {"cod": "135", "msg": "Evento registrado e vinculado a NF-e", "status": "1", "correct": ""}, {"cod": "136", "msg": "Evento registrado, mas não vinculado a NF-e", "status": "1", "correct": ""}, {"cod": "137", "msg": "Nenhum documento localizado para o Destinatário", "status": "1", "correct": ""}, {"cod": "138", "msg": "Documento localizado para o Destinatário", "status": "1", "correct": ""}, {"cod": "139", "msg": "Pedido de Download processado", "status": "1", "correct": ""}, {"cod": "140", "msg": "Download disponibilizado", "status": "1", "correct": ""}, {"cod": "142", "msg": "Ambiente de Contingência EPEC bloqueado para o Emitente", "status": "0", "correct": "EPEC bloqueado consultar a SEFAZ"}, {"cod": "150", "msg": "Autorizado o uso da NF-e, autorização fora de prazo", "status": "1", "correct": ""}, {"cod": "151", "msg": "Cancelamento de NF-e homologado fora de prazo", "status": "1", "correct": ""}, {"cod": "155", "msg": "Cancelamento homologado fora de prazo", "status": "1", "correct": ""}, {"cod": "201", "msg": "Rejeição: Número máximo de numeração a inutilizar ultrapassou o limite", "status": "0", "correct": "Não podem ser inutilizados tantos numeros"}, {"cod": "202", "msg": "Rejeição: Falha no reconhecimento da autoria ou integridade do arquivo digital", "status": "0", "correct": "Algo não esta correto no formato do arquivo"}, {"cod": "203", "msg": "Rejeição: Emissor não habilitado para emissão de NF-e", "status": "0", "correct": "Você não está credenciado para usar esse serviço"}, {"cod": "204", "msg": "Duplicidade de NF-e [nRec:999999999999999]", "status": "0", "correct": "Já existe outra NFe autorizada com esse mesmo numero"}, {"cod": "205", "msg": "NF-e está denegada na base de dados da SEFAZ [nRec:999999999999999]", "status": "0", "correct": "Essa nota já existe e foi DENEGADA"}, {"cod": "206", "msg": "Rejeição: NF-e já está inutilizada na Base de dados da SEFAZ", "status": "0", "correct": "Esse numero de NFe foi inutilizado anteriormente"}, {"cod": "207", "msg": "Rejeição: CNPJ do emitente inválido", "status": "0", "correct": "Erro no CNPJ do emitente"}, {"cod": "208", "msg": "Rejeição: CNPJ do destinatário inválido", "status": "0", "correct": "Erro no CNPJ do destinatário"}, {"cod": "209", "msg": "Rejeição: IE do emitente inválida", "status": "0", "correct": "IE incorreta do emitente"}, {"cod": "210", "msg": "Rejeição: IE do destinatário inválida", "status": "0", "correct": "IE incorreta do destinatário"}, {"cod": "211", "msg": "Rejeição: IE do substituto inválida", "status": "0", "correct": "IE do substituto incorreta"}, {"cod": "212", "msg": "Rejeição: Data de emissão NF-e posterior a data de recebimento", "status": "0", "correct": "Verifique o relogio do sistema ou a zona de tempo ou o horário de verão"}, {"cod": "213", "msg": "Rejeição: CNPJ-Base do Emitente difere do CNPJ-Base do Certificado Digital", "status": "0", "correct": "Você está usando um certificado de outro CNPJ"}, {"cod": "214", "msg": "Rejeição: <PERSON><PERSON><PERSON> da mensagem excedeu o limite estabelecido", "status": "0", "correct": "O tamanho da mensagem é maior que o aceitável, reduza o numero de NFe ou de itens"}, {"cod": "215", "msg": "Rejeição: Falha no schema XML", "status": "0", "correct": "Provavelmente foram encontrados caracteres estranhos e não suportados"}, {"cod": "216", "msg": "Rejeição: <PERSON><PERSON> de Acesso difere da cadastrada", "status": "0", "correct": "A chave de acesso indicada está difernete a registra na SEFAZ"}, {"cod": "217", "msg": "Rejeição: NF-e não consta na base de dados da SEFAZ", "status": "0", "correct": "Essa NFe não está registrada, ou não foi enviada ou foi rejetada"}, {"cod": "218", "msg": "NF-e já está cancelada na base de dados da SEFAZ [nRec:999999999999999]", "status": "0", "correct": "Não é possivel realizar mais operações com a NFE já cancelada"}, {"cod": "219", "msg": "Rejeição: Circulação da NF-e verificada", "status": "0", "correct": "O cancelamento não é mais possivel a circulação já foi identificada"}, {"cod": "220", "msg": "Rejeição: Prazo de Cancelamento superior ao previsto na Legislação", "status": "0", "correct": "As notas somente podem ser canceladas dentro do prazo"}, {"cod": "221", "msg": "Rejeição: Confirmado o recebimento da NF-e pelo destinatário", "status": "0", "correct": "O recebimento já foi feito pelo destinatário não pode mais ser cancelada"}, {"cod": "222", "msg": "Rejeição: Protocolo de Autorização de Uso difere do cadastrado", "status": "0", "correct": "O numero do protocolo indicado é diferente do registrado"}, {"cod": "223", "msg": "Rejeição: CNPJ do transmissor do lote difere do CNPJ do transmissor da consulta", "status": "0", "correct": "O certificado usado para autenticar a comunicação não é o mesmo usado para assinar a NF-e"}, {"cod": "224", "msg": "Rejeição: A faixa inicial é maior que a faixa final", "status": "0", "correct": "Preste atenção aos numeros para inutilizar"}, {"cod": "225", "msg": "Rejeição: Falha no Schema XML do lote de NFe", "status": "0", "correct": "Podem ser Espaços entre as TAGs do XML; Quebras de Linhas; Caracteres especiais; Nome de TAGs errados; Versão do XML diferente do esperado pelo WebService"}, {"cod": "226", "msg": "Rejeição: Código da UF do Emitente diverge da UF autorizadora", "status": "0", "correct": "O cUF indicado não corresponde a UF, avise o administrador"}, {"cod": "227", "msg": "Rejeição: Erro na Chave de Acesso - Campo Id – falta a literal NFe", "status": "0", "correct": "Falha no atributo Id, avise o administrador"}, {"cod": "228", "msg": "Rejeição: Data de Emissão muito atrasada", "status": "0", "correct": "Existe um limite de tempo para enviar uma solicitação e foi ultrapassado. Refaça e reenvie"}, {"cod": "229", "msg": "Rejeição: IE do emitente não informada", "status": "0", "correct": "A IE do emitente é obrigatória"}, {"cod": "230", "msg": "Rejeição: IE do emitente não cadastrada", "status": "0", "correct": "Emitente não cadastrado, verifique com a SEFAZ"}, {"cod": "231", "msg": "Rejeição: IE do emitente não vinculada ao CNPJ", "status": "0", "correct": "A IE não pertence ao CNPJ, corrija"}, {"cod": "232", "msg": "Rejeição: IE do destinatário não informada", "status": "0", "correct": "IE do destinatário deve ser informada nesse caso"}, {"cod": "233", "msg": "Rejeição: IE do destinatário não cadastrada", "status": "0", "correct": "A IE do destinatário deve estar incorreta, verifique"}, {"cod": "234", "msg": "Rejeição: IE do destinatário não vinculada ao CNPJ", "status": "0", "correct": "A IE do destinatário não pertence ao CNPJ do mesmo, verifique"}, {"cod": "235", "msg": "Rejeição: Inscrição SUFRAMA inválida", "status": "0", "correct": "Numero da SUFRAMA incorreto, correija"}, {"cod": "236", "msg": "Rejeição: Chave de Acesso com dígito verificador inválido", "status": "0", "correct": "O digito verificar é calculado e está incorreto, informe o administrador"}, {"cod": "237", "msg": "Rejeição: CPF do destinatário inválido", "status": "0", "correct": "O CFP do destinatário está errdo, corrija"}, {"cod": "238", "msg": "Rejeição: Cabeçalho - Versão do arquivo XML superior a Versão vigente", "status": "0", "correct": "O numero da versão está incorreto, informe o administrador"}, {"cod": "239", "msg": "Rejeição: Cabeçalho - Versão do arquivo XML não suportada", "status": "0", "correct": "Versão do documento errada, informe o adminstrador"}, {"cod": "240", "msg": "Rejeição: Cancelamento/Inutilização - Irregularidade Fiscal do Emitente", "status": "0", "correct": "Consulte a SEFAZ"}, {"cod": "241", "msg": "Rejeição: Um número da faixa já foi utilizado", "status": "0", "correct": "Essa inutilização já foi realizada."}, {"cod": "242", "msg": "Rejeição: Cabeçalho - Falha no Schema XML", "status": "0", "correct": "Normalmente causado por caracteres inválidos, informe o administrador"}, {"cod": "243", "msg": "Rejeição: XML Mal Formado", "status": "0", "correct": "Erro na montagem do XML, informe o administrador"}, {"cod": "244", "msg": "Rejeição: CNPJ do Certificado Digital difere do CNPJ da Matriz e do CNPJ do Emitente", "status": "0", "correct": "O certificado utilizado é incorreto"}, {"cod": "245", "msg": "Rejeição: CNPJ Emitente não cadastrado", "status": "0", "correct": "Confira o CNPJ do emitente está errado"}, {"cod": "246", "msg": "Rejeição: CNPJ Destinatário não cadastrado", "status": "0", "correct": "Confira o CNPJ do destinatário está errado"}, {"cod": "247", "msg": "Rejeição: Sigla da UF do Emitente diverge da UF autorizadora", "status": "0", "correct": "O sistema está enviando para outro autorizador diverso da UF do emitente "}, {"cod": "248", "msg": "Rejeição: UF do Recibo diverge da UF autorizadora", "status": "0", "correct": "O recibo usado é de outra autoriazadora, verifique qual é o recibo correto"}, {"cod": "249", "msg": "Rejeição: UF da Chave de Acesso diverge da UF autorizadora", "status": "0", "correct": "A chave de acesso é de outra autorizadora"}, {"cod": "250", "msg": "Rejeição: UF diverge da UF autorizadora", "status": "0", "correct": ""}, {"cod": "251", "msg": "Rejeição: UF/Município destinatário não pertence a SUFRAMA", "status": "0", "correct": ""}, {"cod": "252", "msg": "Rejeição: Ambiente informado diverge do Ambiente de recebimento", "status": "0", "correct": ""}, {"cod": "253", "msg": "Rejeição: <PERSON><PERSON>o Verificador da chave de acesso composta inválida", "status": "0", "correct": ""}, {"cod": "254", "msg": "Rejeição: NF-e complementar não possui NF referenciada", "status": "0", "correct": ""}, {"cod": "255", "msg": "Rejeição: NF-e complementar possui mais de uma NF referenciada", "status": "0", "correct": ""}, {"cod": "256", "msg": "Rejeição: Uma NF-e da faixa já está inutilizada na Base de dados da SEFAZ", "status": "0", "correct": ""}, {"cod": "257", "msg": "Rejeição: Solicitante não habilitado para emissão da NF-e", "status": "0", "correct": ""}, {"cod": "258", "msg": "Rejeição: CNPJ da consulta inválido", "status": "0", "correct": ""}, {"cod": "259", "msg": "Rejeição: CNPJ da consulta não cadastrado como contribuinte na UF", "status": "0", "correct": ""}, {"cod": "260", "msg": "Rejeição: IE da consulta inválida", "status": "0", "correct": ""}, {"cod": "261", "msg": "Rejeição: IE da consulta não cadastrada como contribuinte na UF", "status": "0", "correct": ""}, {"cod": "262", "msg": "Rejeição: UF não fornece consulta por CPF", "status": "0", "correct": ""}, {"cod": "263", "msg": "Rejeição: CPF da consulta inválido", "status": "0", "correct": ""}, {"cod": "264", "msg": "Rejeição: CPF da consulta não cadastrado como contribuinte na UF", "status": "0", "correct": ""}, {"cod": "265", "msg": "Rejeição: Sigla da UF da consulta difere da UF do Web Service", "status": "0", "correct": ""}, {"cod": "266", "msg": "Rejeição: Série utilizada não permitida no Web Service", "status": "0", "correct": ""}, {"cod": "267", "msg": "Rejeição: NF Complementar referencia uma NF-e inexistente", "status": "0", "correct": ""}, {"cod": "268", "msg": "Rejeição: NF Complementar referencia outra NF-e Complementar", "status": "0", "correct": ""}, {"cod": "269", "msg": "Rejeição: CNPJ Emitente da NF Complementar difere do CNPJ da NF Referenciada", "status": "0", "correct": ""}, {"cod": "270", "msg": "Rejeição: <PERSON><PERSON>digo Municí<PERSON> do Fato Gerador: dígito inválido", "status": "0", "correct": ""}, {"cod": "271", "msg": "Rejeição: Código Município do Fato Gerador: difere da UF do emitente", "status": "0", "correct": ""}, {"cod": "272", "msg": "Rejeição: Código Município do Emitente: dígito inválido", "status": "0", "correct": ""}, {"cod": "273", "msg": "Rejeição: Código Município do Emitente: difere da UF do emitente", "status": "0", "correct": ""}, {"cod": "274", "msg": "Rejeição: Código Município do Destinatário: dígito inválido", "status": "0", "correct": ""}, {"cod": "275", "msg": "Rejeição: Código Município do Destinatário: difere da UF do Destinatário", "status": "0", "correct": ""}, {"cod": "276", "msg": "Rejeição: Código Município do Local de Retirada: dígito inválido", "status": "0", "correct": ""}, {"cod": "277", "msg": "Rejeição: Código Município do Local de Retirada: difere da UF do Local de Retirada", "status": "0", "correct": ""}, {"cod": "278", "msg": "Rejeição: Código Município do Local de Entrega: dígito inválido", "status": "0", "correct": ""}, {"cod": "279", "msg": "Rejeição: Código Município do Local de Entrega: difere da UF do Local de Entrega", "status": "0", "correct": ""}, {"cod": "280", "msg": "Rejeição: Certificado Transmissor inválido", "status": "0", "correct": ""}, {"cod": "281", "msg": "Rejeição: Certificado Transmissor Data Validade", "status": "0", "correct": ""}, {"cod": "282", "msg": "Rejeição: Certificado Transmissor sem CNPJ", "status": "0", "correct": ""}, {"cod": "283", "msg": "Rejeição: Certificado Transmissor - erro Cadeia de Certificação", "status": "0", "correct": ""}, {"cod": "284", "msg": "Rejeição: Certificado Transmissor revogado", "status": "0", "correct": ""}, {"cod": "285", "msg": "Rejeição: Certificado Transmissor difere ICP-Brasil", "status": "0", "correct": ""}, {"cod": "286", "msg": "Rejeição: Certificado Transmissor erro no acesso a LCR", "status": "0", "correct": ""}, {"cod": "287", "msg": "Rejeição: Código Município do FG - ISSQN: dígito inválido", "status": "0", "correct": ""}, {"cod": "288", "msg": "Rejeição: Código Município do FG - Transporte: dígito inválido", "status": "0", "correct": ""}, {"cod": "289", "msg": "Rejeição: Código da UF informada diverge da UF solicitada", "status": "0", "correct": ""}, {"cod": "290", "msg": "Rejeição: Certificado Assinatura inválido", "status": "0", "correct": ""}, {"cod": "291", "msg": "Rejeição: Certificado Assinatura Data Validade", "status": "0", "correct": ""}, {"cod": "292", "msg": "Rejeição: Certificado Assinatura sem CNPJ", "status": "0", "correct": ""}, {"cod": "293", "msg": "Rejeição: Certificado Assinatura - erro Cadeia de Certificação", "status": "0", "correct": ""}, {"cod": "294", "msg": "Rejeição: Certificado Assinatura revogado", "status": "0", "correct": ""}, {"cod": "295", "msg": "Rejeição: Certificado Assinatura difere ICP-Brasil", "status": "0", "correct": ""}, {"cod": "296", "msg": "Rejeição: Certificado Assinatura erro no acesso a LCR", "status": "0", "correct": ""}, {"cod": "297", "msg": "Rejeição: Assinatura difere do calculado", "status": "0", "correct": ""}, {"cod": "298", "msg": "Rejeição: Assinatura difere do padrão do Sistema", "status": "0", "correct": ""}, {"cod": "299", "msg": "Rejeição: XML da área de cabeçalho com codificação diferente de UTF-8", "status": "0", "correct": ""}, {"cod": "301", "msg": "Uso Denegado: Irregularidade fiscal do emitente", "status": "0", "correct": ""}, {"cod": "302", "msg": "Uso Denegado: Irregularidade fiscal do destinatário", "status": "0", "correct": ""}, {"cod": "303", "msg": "Uso Denegado: Destinatário não habilitado a operar na UF", "status": "0", "correct": ""}, {"cod": "304", "msg": "Rejeição: Pedido de Cancelamento para NF-e com evento da Suframa", "status": "0", "correct": ""}, {"cod": "315", "msg": "Data de Emissão anterior ao início da autorização de Nota Fiscal na UF", "status": "0", "correct": ""}, {"cod": "316", "msg": "Nota Fiscal referenciada com a mesma Chave de Acesso da Nota Fiscal atual", "status": "0", "correct": ""}, {"cod": "317", "msg": "NF modelo 1 referenciada com data de emissão inválida", "status": "0", "correct": ""}, {"cod": "318", "msg": "Contranota de Produtor sem Nota Fiscal referenciada", "status": "0", "correct": ""}, {"cod": "319", "msg": "Contranota de Produtor não pode referenciar somente Nota Fiscal de entrada", "status": "0", "correct": ""}, {"cod": "320", "msg": "Contranota de Produtor referencia somente NF de outro emitente", "status": "0", "correct": ""}, {"cod": "321", "msg": "Rejeição: NF-e de devolução de mercadoria não possui documento fiscal referenciado", "status": "0", "correct": ""}, {"cod": "322", "msg": "NF-e de devolução com mais de um documento fiscal referenciado", "status": "0", "correct": ""}, {"cod": "323", "msg": "Rejeição: CNPJ autorizado para download inválido", "status": "0", "correct": ""}, {"cod": "324", "msg": "Rejeição: CNPJ do destinatário já autorizado para download", "status": "0", "correct": ""}, {"cod": "325", "msg": "Rejeição: CPF autorizado para download inválido", "status": "0", "correct": ""}, {"cod": "326", "msg": "Rejeição: CPF do destinatário já autorizado para download", "status": "0", "correct": ""}, {"cod": "327", "msg": "Rejeição: CFOP inválido para NF-e com finalidade de devolução de mercadoria", "status": "0", "correct": ""}, {"cod": "328", "msg": "Rejeição: CFOP de devolução de mercadoria para NF-e que não tem finalidade de devolução de mercadoria", "status": "0", "correct": ""}, {"cod": "329", "msg": "Rejeição: Número da DI /DSI inválido", "status": "0", "correct": ""}, {"cod": "330", "msg": "Rejeição: Informar o Valor da AFRMM na importação por via marítima", "status": "0", "correct": ""}, {"cod": "331", "msg": "Rejeição: Informar o CNPJ do adquirente ou do encomendante nesta forma de importação", "status": "0", "correct": ""}, {"cod": "332", "msg": "Rejeição: CNPJ do adquirente ou do encomendante da importação inválido", "status": "0", "correct": ""}, {"cod": "333", "msg": "Rejeição: Informar a UF do adquirente ou do encomendante nesta forma de importação", "status": "0", "correct": ""}, {"cod": "334", "msg": "Rejeição: Número do processo de drawback não informado na importação", "status": "0", "correct": ""}, {"cod": "335", "msg": "Rejeição: Número do processo de drawback na importação inválido", "status": "0", "correct": ""}, {"cod": "336", "msg": "Rejeição: Informado o grupo de exportação no item para CFOP que não é de exportação", "status": "0", "correct": ""}, {"cod": "337", "msg": "Rejeição: Não informado o grupo de exportação no item", "status": "0", "correct": ""}, {"cod": "338", "msg": "Rejeição: Número do processo de drawback não informado na exportação", "status": "0", "correct": ""}, {"cod": "339", "msg": "Rejeição: Número do processo de drawback na exportação inválido", "status": "0", "correct": ""}, {"cod": "340", "msg": "Rejeição: Não informado o grupo de exportação indireta no item", "status": "0", "correct": ""}, {"cod": "341", "msg": "Rejeição: Número do registro de exportação inválido", "status": "0", "correct": ""}, {"cod": "342", "msg": "Rejeição: Chave de Acesso informada na Exportação Indireta com DV inválido", "status": "0", "correct": ""}, {"cod": "343", "msg": "Rejeição: Modelo da NF-e informada na Exportação Indireta diferente de 55", "status": "0", "correct": ""}, {"cod": "344", "msg": "Rejeição: Duplicidade de NF-e informada na Exportação Indireta (Chave de Acesso informada mais de uma vez)", "status": "0", "correct": ""}, {"cod": "345", "msg": "Rejeição: Chave de Acesso informada na Exportação Indireta não consta como NF-e referenciada", "status": "0", "correct": ""}, {"cod": "346", "msg": "Rejeição: Somatório das quantidades informadas na Exportação Indireta não corresponde a quantidade total do item", "status": "0", "correct": ""}, {"cod": "347", "msg": "Rejeição: Descrição do Combustível diverge da descrição adotada pela ANP", "status": "0", "correct": ""}, {"cod": "348", "msg": "Rejeição: NFC-e com grupo RECOPI", "status": "0", "correct": ""}, {"cod": "349", "msg": "Rejeição: Número RECOPI não informado", "status": "0", "correct": ""}, {"cod": "350", "msg": "Rejeição: Número RECOPI inválido", "status": "0", "correct": ""}, {"cod": "351", "msg": "Rejeição: Valor do ICMS da Operação no CST=51 difere do produto BC e Alíquota", "status": "0", "correct": ""}, {"cod": "352", "msg": "Rejeição: Valor do ICMS Diferido no CST=51 difere do produto Valor ICMS Operação e percentual diferimento", "status": "0", "correct": ""}, {"cod": "353", "msg": "Rejeição: Valor do ICMS no CST=51 não corresponde a diferença do ICMS operação e ICMS diferido", "status": "0", "correct": ""}, {"cod": "354", "msg": "Rejeição: Informado grupo de devolução de tributos para NF-e que não tem finalidade de devolução de mercadoria", "status": "0", "correct": ""}, {"cod": "355", "msg": "Rejeição: Informar o local de saída do Pais no caso da exportação", "status": "0", "correct": ""}, {"cod": "356", "msg": "Rejeição: Informar o local de saída do Pais somente no caso da exportação", "status": "0", "correct": ""}, {"cod": "357", "msg": "Rejeição: Chave de Acesso do grupo de Exportação Indireta inexistente [nRef: xxx]", "status": "0", "correct": ""}, {"cod": "358", "msg": "Rejeição: Chave de Acesso do grupo de Exportação Indireta cancelada ou denegada [nRef: xxx]", "status": "0", "correct": ""}, {"cod": "359", "msg": "Rejeição: NF-e de venda a Órgão Público sem informar a Nota de Empenho", "status": "0", "correct": ""}, {"cod": "360", "msg": "Rejeição: NF-e com Nota de Empenho inválida para a UF.", "status": "0", "correct": ""}, {"cod": "361", "msg": "Rejeição: NF-e com Nota de Empenho inexistente na UF.", "status": "0", "correct": ""}, {"cod": "362", "msg": "Rejeição: Venda de combustível sem informação do Transportador", "status": "0", "correct": ""}, {"cod": "363", "msg": "Total do ISS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "364", "msg": "Rejeição: Total do valor da dedução do ISS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "365", "msg": "Rejeição: Total de outras retenções difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "366", "msg": "Rejeição: Total do desconto incondicionado ISS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "367", "msg": "Rejeição: Total do desconto condicionado ISS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "368", "msg": "Rejeição: Total de ISS retido difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "369", "msg": "Rejeição: Não informado o grupo avulsa na emissão pelo Fisco", "status": "0", "correct": ""}, {"cod": "370", "msg": "Rejeição: Nota Fiscal Avulsa com tipo de emissão inválido", "status": "0", "correct": ""}, {"cod": "372", "msg": "Destinatário com identificação de estrangeiro com caracteres inválidos", "status": "0", "correct": ""}, {"cod": "373", "msg": "Descricao do primeiro item diferente de NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL", "status": "0", "correct": ""}, {"cod": "374", "msg": "CFOP incompatível com o grupo de tributação [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "375", "msg": "Informe no passo 1 a chave da NF-e referenciada.NF-e com CFOP 5929 (Lançamento relativo a Cupom Fiscal) referencia uma NFC-e [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "376", "msg": "Data do Desembaraço Aduaneiro inválida [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "378", "msg": "Grupo de Combustível sem a informação de Encerrante [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "379", "msg": "Grupo de Encerrante na NF-e (modelo 55) para CFOP diferente de venda de combustível para consumidor final [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "380", "msg": "Valor do Encerrante final não é superior ao Encerrante inicial [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "381", "msg": "Grupo de tributação ICMS90, informando dados do ICMS-ST [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "382", "msg": "CFOP não permitido para o CST informado [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "383", "msg": "Item com CSOSN indevido [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "384", "msg": "CSOSN não permitido para a UF [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "385", "msg": "Grupo de tributação ICMS900, informando dados do ICMS-ST [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "386", "msg": "CFOP não permitido para o CSOSN informado [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "387", "msg": "Código de Enquadramento Legal do IPI inválido [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "388", "msg": "Código de Situação Tributária do IPI incompatível com o Código de Enquadramento Legal do IPI [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "389", "msg": "Código Município ISSQN inexistente [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "390", "msg": "Nota Fiscal com grupo de devolução de tributos [nItem:nnn]", "status": "0", "correct": ""}, {"cod": "391", "msg": "Não informados os dados do cartão de crédito / débito nas Formas de Pagamento da Nota Fiscal", "status": "0", "correct": ""}, {"cod": "392", "msg": "Não informados os dados da operação de pagamento por cartão de crédito / débito", "status": "0", "correct": ""}, {"cod": "393", "msg": "NF-e com o grupo de Informações Suplementares", "status": "0", "correct": ""}, {"cod": "394", "msg": "Nota Fiscal sem a informação do QR-Code", "status": "0", "correct": ""}, {"cod": "395", "msg": "Endereço do site da UF da Consulta via QRCode diverge do previsto", "status": "0", "correct": ""}, {"cod": "396", "msg": "Parâmetro do QR-Code inexistente (chAcesso)", "status": "0", "correct": ""}, {"cod": "397", "msg": "Parâmetro do QR-Code divergente da Nota Fiscal (chAcesso)", "status": "0", "correct": ""}, {"cod": "398", "msg": "Parâmetro nVersao do QR-Code difere do previsto", "status": "0", "correct": ""}, {"cod": "399", "msg": "Parâmetro de Identificação do destinatário no QR-Code para Nota Fiscal sem identificação do destinatário", "status": "0", "correct": ""}, {"cod": "401", "msg": "Rejeição: CPF do remetente inválido", "status": "0", "correct": ""}, {"cod": "402", "msg": "Rejeição: XML da área de dados com codificação diferente de UTF-8", "status": "0", "correct": ""}, {"cod": "403", "msg": "Rejeição: O grupo de informações da NF-e avulsa é de uso exclusivo do Fisco", "status": "0", "correct": ""}, {"cod": "404", "msg": "Rejeição: Uso de prefixo de namespace não permitido", "status": "0", "correct": ""}, {"cod": "405", "msg": "Rejeição: Código do país do emitente: dígito inválido", "status": "0", "correct": ""}, {"cod": "406", "msg": "Rejeição: Código do país do destinatário: dígito inválido", "status": "0", "correct": ""}, {"cod": "407", "msg": "Rejeição: O CPF só pode ser informado no campo emitente para a NF-e avulsa", "status": "0", "correct": ""}, {"cod": "408", "msg": "Rejeição: Evento não disponível para Autor pessoa física", "status": "0", "correct": ""}, {"cod": "409", "msg": "Rejeição: Campo cUF inexistente no elemento nfeCabecMsg do SOAP Header", "status": "0", "correct": ""}, {"cod": "410", "msg": "Rejeição: UF informada no campo cUF não é atendida pelo Web Service", "status": "0", "correct": ""}, {"cod": "411", "msg": "Rejeição: Campo versaoDados inexistente no elemento nfeCabecMsg do SOAP Header", "status": "0", "correct": ""}, {"cod": "416", "msg": "Rejeição: Falha na descompactação da área de dados", "status": "0", "correct": ""}, {"cod": "417", "msg": "Rejeição: Total do ICMS superior ao valor limite estabelecido", "status": "0", "correct": ""}, {"cod": "418", "msg": "Rejeição: Total do ICMS ST superior ao valor limite estabelecido", "status": "0", "correct": ""}, {"cod": "420", "msg": "Rejeição: Cancelamento para NF-e já cancelada", "status": "0", "correct": ""}, {"cod": "450", "msg": "Rejeição: Modelo da NF-e diferente de 55", "status": "0", "correct": ""}, {"cod": "451", "msg": "Rejeição: Processo de emissão informado inválido", "status": "0", "correct": ""}, {"cod": "452", "msg": "Rejeição: Tipo Autorizador do Recibo diverge do Órgão Autorizador", "status": "0", "correct": ""}, {"cod": "453", "msg": "Rejeição: Ano de inutilização não pode ser superior ao Ano atual", "status": "0", "correct": ""}, {"cod": "454", "msg": "Rejeição: Ano de inutilização não pode ser inferior a 2006", "status": "0", "correct": ""}, {"cod": "455", "msg": "Rejeição: Órgão Autor do evento diferente da UF da Chave de Acesso", "status": "0", "correct": ""}, {"cod": "461", "msg": "Rejeição: Informado percentual de Gás Natural na mistura para produto diferente de GLP", "status": "0", "correct": ""}, {"cod": "462", "msg": "Código Identificador do CSC no QR-Code não cadastrado na SEFAZ", "status": "0", "correct": ""}, {"cod": "463", "msg": "Código Identificador do CSC no QR-Code foi revogado pela empresa", "status": "0", "correct": ""}, {"cod": "464", "msg": "Código de Hash no QR-Code difere do calculado", "status": "0", "correct": ""}, {"cod": "465", "msg": "Rejeição: Número de Controle da FCI inexistente", "status": "0", "correct": ""}, {"cod": "466", "msg": "Rejeição: Evento com Tipo de Autor incompatível", "status": "0", "correct": ""}, {"cod": "467", "msg": "Rejeição: Dados da NF-e divergentes do EPEC", "status": "0", "correct": ""}, {"cod": "468", "msg": "Rejeição: NF-e com Tipo Emissão = 4, sem EPEC correspondente", "status": "0", "correct": ""}, {"cod": "471", "msg": "Rejeição: Informado NCM=00 indevidamente", "status": "0", "correct": ""}, {"cod": "476", "msg": "Rejeição: <PERSON><PERSON><PERSON> da UF diverge da UF da primeira NF-e do Lote", "status": "0", "correct": ""}, {"cod": "477", "msg": "Rejeição: Código do órgão diverge do órgão do primeiro evento do Lote", "status": "0", "correct": ""}, {"cod": "478", "msg": "Rejeição: Local da entrega não informado para faturamento direto de veículos novos", "status": "0", "correct": ""}, {"cod": "479", "msg": "Emissor em situação irregular perante o fisco", "status": "", "correct": ""}, {"cod": "480", "msg": "CNPJ da Chave de acesso da NF-e informada diverge do CNPJ do emitente", "status": "", "correct": ""}, {"cod": "481", "msg": "UF da Chave de acesso diverge do código da UF informada", "status": "", "correct": ""}, {"cod": "482", "msg": "AA da Chave de acesso inválida", "status": "", "correct": ""}, {"cod": "483", "msg": "MM da chave de acesso inválido", "status": "", "correct": ""}, {"cod": "484", "msg": "Rejeição: Chave de Acesso com tipo de emissão diferente de 4 (posição 35 da Chave de Acesso)", "status": "0", "correct": ""}, {"cod": "485", "msg": "Rejeição: Duplicidade de numeração do EPEC (Modelo, CNPJ, Série e Número)", "status": "0", "correct": ""}, {"cod": "486", "msg": "DPEC não localizada para o número de registro de DPEC informado", "status": "0", "correct": ""}, {"cod": "487", "msg": "Nenhuma DPEC localizada para a chave de acesso informada", "status": "0", "correct": ""}, {"cod": "488", "msg": "Requisitante de Consulta não tem o mesmo CNPJ base do emissor da DPEC", "status": "0", "correct": ""}, {"cod": "489", "msg": "Rejeição: CNPJ informado inválid<PERSON> (DV ou zeros)", "status": "0", "correct": ""}, {"cod": "490", "msg": "Rejeição: CPF informado inválido (DV ou zeros)", "status": "0", "correct": ""}, {"cod": "491", "msg": "Rejeição: O tpEvento informado inválido", "status": "0", "correct": ""}, {"cod": "492", "msg": "Rejeição: O verEvento informado inválido", "status": "0", "correct": ""}, {"cod": "493", "msg": "Rejeição: Evento não atende o Schema XML específico", "status": "0", "correct": ""}, {"cod": "494", "msg": "Rejeição: <PERSON><PERSON> inexistente", "status": "0", "correct": ""}, {"cod": "496", "msg": "Não informado o tipo de integração no pagamento com cartão de crédito / débito", "status": "0", "correct": ""}, {"cod": "501", "msg": "Rejeição: Pedido de Cancelamento intempestivo (NF-e autorizada a mais de 7 dias)", "status": "0", "correct": ""}, {"cod": "502", "msg": "Rejeição: Erro na Chave de Acesso - Campo Id não corresponde à concatenação dos campos correspondentes", "status": "0", "correct": ""}, {"cod": "503", "msg": "Rejeição: Série utilizada fora da faixa permitida no SCAN (900-999)", "status": "0", "correct": ""}, {"cod": "504", "msg": "Rejeição: Data de Entrada/Saída posterior ao permitido", "status": "0", "correct": ""}, {"cod": "505", "msg": "Rejeição: Data de Entrada/Saída anterior ao permitido", "status": "0", "correct": ""}, {"cod": "506", "msg": "Rejeição: Data de Saída menor que a Data de Emissão", "status": "0", "correct": ""}, {"cod": "507", "msg": "Rejeição: O CNPJ do destinatário/remetente não deve ser informado em operação com o exterior", "status": "0", "correct": ""}, {"cod": "508", "msg": "Rejeição: CNPJ do destinatário com conteúdo nulo só é válido em operação com exterior", "status": "0", "correct": ""}, {"cod": "509", "msg": "Rejeição: Informado código de município diferente de “9999999” para operação com o exterior", "status": "0", "correct": ""}, {"cod": "510", "msg": "Rejeição: Operação com Exterior e Código País destinatário é 1058 (Brasil) ou não informado", "status": "0", "correct": ""}, {"cod": "511", "msg": "Rejeição: Não é de Operação com Exterior e Código País destinatário difere de 1058 (Brasil)", "status": "0", "correct": ""}, {"cod": "512", "msg": "Rejeição: CNPJ do Local de Retirada inválido", "status": "0", "correct": ""}, {"cod": "513", "msg": "Rejeição: <PERSON>ódigo Municí<PERSON> do Local de Retirada deve ser 9999999 para UF retirada = EX", "status": "0", "correct": ""}, {"cod": "514", "msg": "Rejeição: CNPJ do Local de Entrega inválido", "status": "0", "correct": ""}, {"cod": "515", "msg": "Rejeição: Código Municí<PERSON> do Local de Entrega deve ser 9999999 para UF entrega = EX", "status": "0", "correct": ""}, {"cod": "516", "msg": "Rejeição: Falha no schema XML – inexiste a tag raiz esperada para a mensagem", "status": "0", "correct": ""}, {"cod": "517", "msg": "Rejeição: Falha no schema XML – inexiste atributo versao na tag raiz da mensagem", "status": "0", "correct": ""}, {"cod": "518", "msg": "Rejeição: CFOP de entrada para NF-e de saída", "status": "0", "correct": ""}, {"cod": "519", "msg": "Rejeição: CFOP de saída para NF-e de entrada", "status": "0", "correct": ""}, {"cod": "520", "msg": "Rejeição: CFOP de Operação com Exterior e UF destinatário difere de EX", "status": "0", "correct": ""}, {"cod": "521", "msg": "Rejeição: CFOP de Operação Estadual e UF do emitente difere da UF do destinatário para destinatário contribuinte do ICMS.", "status": "0", "correct": ""}, {"cod": "522", "msg": "Rejeição: CFOP de Operação Estadual e UF emitente difere da UF remetente para remetente contribuinte do ICMS.", "status": "0", "correct": ""}, {"cod": "523", "msg": "Rejeição: CFOP não é de Operação Estadual e UF emitente igual a UF destinatário.", "status": "0", "correct": ""}, {"cod": "524", "msg": "Rejeição: CFOP de Operação com Exterior e não informado NCM", "status": "0", "correct": ""}, {"cod": "525", "msg": "Rejeição: CFOP de Importação e não informado dados da DI", "status": "0", "correct": ""}, {"cod": "526", "msg": "Ano-Mes da Chave de Acesso com atraso superior a 6 meses em relacao ao Ano-Mes atual", "status": "0", "correct": ""}, {"cod": "527", "msg": "Rejeição: Operação de Exportação com informação de ICMS incompatível", "status": "0", "correct": ""}, {"cod": "528", "msg": "Rejeição: Valor do ICMS difere do produto BC e Alíquota", "status": "0", "correct": ""}, {"cod": "529", "msg": "Rejeição: NCM de informação obrigatória para produto tributado pelo IPI", "status": "0", "correct": ""}, {"cod": "530", "msg": "Rejeição: Operação com tributação de ISSQN sem informar a Inscrição Municipal", "status": "0", "correct": ""}, {"cod": "531", "msg": "Rejeição: Total da BC ICMS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "532", "msg": "Rejeição: Total do ICMS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "533", "msg": "Rejeição: Total da BC ICMS-ST difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "534", "msg": "Rejeição: Total do ICMS-ST difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "535", "msg": "Rejeição: Total do Frete difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "536", "msg": "Rejeição: Total do Seguro difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "537", "msg": "Rejeição: Total do Desconto difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "538", "msg": "Rejeição: Total do IPI difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "539", "msg": "Duplicidade de NF-e com diferença na Chave de Acesso [chNFe: 99999999999999999999999999999999999999999999][nRec:999999999999999]", "status": "0", "correct": ""}, {"cod": "540", "msg": "Rejeição: CPF do Local de Retirada inválido", "status": "0", "correct": ""}, {"cod": "541", "msg": "Rejeição: CPF do Local de Entrega inválido", "status": "0", "correct": ""}, {"cod": "542", "msg": "Rejeição: CNPJ do Transportador inválido", "status": "0", "correct": ""}, {"cod": "543", "msg": "Rejeição: CPF do Transportador inválido", "status": "0", "correct": ""}, {"cod": "544", "msg": "Rejeição: IE do Transportador inválida", "status": "0", "correct": ""}, {"cod": "545", "msg": "Rejeição: Falha no schema XML – versão informada na versaoDados do SOAPHeader diverge da versão da mensagem", "status": "0", "correct": ""}, {"cod": "546", "msg": "Rejeição: Erro na Chave de Acesso – Campo Id – falta a literal NFe", "status": "0", "correct": ""}, {"cod": "547", "msg": "Rejeição: Dígito Verificador da Chave de Acesso da NF-e Referenciada inválido", "status": "0", "correct": ""}, {"cod": "548", "msg": "Rejeição: CNPJ da NF referenciada inválido.", "status": "0", "correct": ""}, {"cod": "549", "msg": "Rejeição: CNPJ da NF referenciada de produtor inválido.", "status": "0", "correct": ""}, {"cod": "550", "msg": "Rejeição: CPF da NF referenciada de produtor inválido.", "status": "0", "correct": ""}, {"cod": "551", "msg": "Rejeição: IE da NF referenciada de produtor inválido.", "status": "0", "correct": ""}, {"cod": "552", "msg": "Rejeição: Dígito Verificador da Chave de Acesso do CT-e Referenciado inválido", "status": "0", "correct": ""}, {"cod": "553", "msg": "Rejeição: Tipo autorizador do recibo diverge do Órgão Autorizador.", "status": "0", "correct": ""}, {"cod": "554", "msg": "Rejeição: Série difere da faixa 0-899", "status": "0", "correct": ""}, {"cod": "555", "msg": "Rejeição: Tipo autorizador do protocolo diverge do Órgão Autorizador.", "status": "0", "correct": ""}, {"cod": "556", "msg": "Rejeição: Justificativa de entrada em contingência não deve ser informada para tipo de emissão normal", "status": "0", "correct": ""}, {"cod": "557", "msg": "Rejeição: A Justificativa de entrada em contingência deve ser informada.", "status": "0", "correct": ""}, {"cod": "558", "msg": "Rejeição: Data de entrada em contingência posterior a data de recebimento.", "status": "0", "correct": ""}, {"cod": "559", "msg": "Rejeição: UF do Transportador não informada", "status": "0", "correct": ""}, {"cod": "560", "msg": "Rejeição: CNPJ base do emitente difere do CNPJ base da primeira NF-e do lote recebido", "status": "0", "correct": ""}, {"cod": "561", "msg": "Rejeição: Mês de Emissão informado na Chave de Acesso difere do Mês de Emissão da NF-e", "status": "0", "correct": ""}, {"cod": "562", "msg": "Rejeição: <PERSON><PERSON><PERSON> Numérico informado na Chave de Acesso difere do Código Numérico da NF-e [chNFe:99999999999999999999999999999999999999999999]", "status": "0", "correct": ""}, {"cod": "563", "msg": "Rejeição: Já existe pedido de Inutilização com a mesma faixa de inutilização", "status": "0", "correct": ""}, {"cod": "564", "msg": "Rejeição: Total do Produto / Serviço difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "565", "msg": "Rejeição: Falha no schema XML – inexiste a tag raiz esperada para o lote de NF-e", "status": "0", "correct": ""}, {"cod": "567", "msg": "Rejeição: Falha no schema XML – versão informada na versaoDados do SOAPHeader diverge da versão do lote de NF-e", "status": "0", "correct": ""}, {"cod": "568", "msg": "Rejeição: Falha no schema XML – inexiste atributo versao na tag raiz do lote de NF-e", "status": "0", "correct": ""}, {"cod": "569", "msg": "Rejeição: Data de entrada em contingência muito atrasada", "status": "0", "correct": ""}, {"cod": "570", "msg": "Rejeição: Tipo de Emissão 3, 6 ou 7 só é válido nas contingências SCAN/SVC", "status": "0", "correct": ""}, {"cod": "571", "msg": "Rejeição: O tpEmis informado diferente de 3 para contingência SCAN", "status": "0", "correct": ""}, {"cod": "572", "msg": "Rejeição: Erro Atributo ID do evento não corresponde a concatenação dos campos (“ID” + tpEvento + chNFe + nSeqEvento)", "status": "0", "correct": ""}, {"cod": "573", "msg": "Rejeição: Duplicidade de Evento", "status": "0", "correct": ""}, {"cod": "574", "msg": "Rejeição: O autor do evento diverge do emissor da NF-e", "status": "0", "correct": ""}, {"cod": "575", "msg": "Rejeição: O autor do evento diverge do destinatário da NF-e", "status": "0", "correct": ""}, {"cod": "576", "msg": "Rejeição: O autor do evento não é um órgão autorizado a gerar o evento", "status": "0", "correct": ""}, {"cod": "577", "msg": "Rejeição: A data do evento não pode ser menor que a data de emissão da NF-e", "status": "0", "correct": ""}, {"cod": "578", "msg": "Rejeição: A data do evento não pode ser maior que a data do processamento", "status": "0", "correct": ""}, {"cod": "579", "msg": "Rejeição: A data do evento não pode ser menor que a data de autorização para NF-e não emitida em contingência", "status": "0", "correct": ""}, {"cod": "580", "msg": "Rejeição: O evento exige uma NF-e autorizada", "status": "0", "correct": ""}, {"cod": "587", "msg": "Rejeição: Usar somente o namespace padrão da NF-e", "status": "0", "correct": ""}, {"cod": "588", "msg": "Rejeição: Não é permitida a presença de caracteres de edição no início/fim da mensagem ou entre as tags da mensagem", "status": "0", "correct": ""}, {"cod": "589", "msg": "Rejeição: Número do NSU informado superior ao maior NSU da base de dados da SEFAZ", "status": "0", "correct": ""}, {"cod": "590", "msg": "Rejeição: Informado CST para emissor do Simples Nacional (CRT=1)", "status": "0", "correct": ""}, {"cod": "591", "msg": "Rejeição: Informado CSOSN para emissor que não é do Simples Nacional (CRT diferente de 1)", "status": "0", "correct": ""}, {"cod": "592", "msg": "Rejeição: A NF-e deve ter pelo menos um item de produto sujeito ao ICMS", "status": "0", "correct": ""}, {"cod": "593", "msg": "Rejeição: CNPJ-Base consultado difere do CNPJ-Base do Certificado Digital", "status": "0", "correct": ""}, {"cod": "594", "msg": "Rejeição: O número de sequencia do evento informado é maior que o permitido", "status": "0", "correct": ""}, {"cod": "595", "msg": "Rejeição: Obrigatória a informação da justificativa do evento.", "status": "0", "correct": ""}, {"cod": "596", "msg": "Rejeição: Evento apresentado fora do prazo: [prazo vigente]", "status": "0", "correct": ""}, {"cod": "597", "msg": "Rejeição: CFOP de Importação e não informado dados de IPI", "status": "0", "correct": ""}, {"cod": "598", "msg": "Rejeição: NF-e emitida em ambiente de homologação com Razão Social do destinatário diferente de NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL", "status": "0", "correct": ""}, {"cod": "599", "msg": "Rejeição: CFOP de Importação e não informado dados de II", "status": "0", "correct": ""}, {"cod": "600", "msg": "CSOSN incompativel na operacao com Nao Contribuinte", "status": "", "correct": ""}, {"cod": "601", "msg": "Rejeição: Total do II difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "602", "msg": "Rejeição: Total do PIS difere do somatório dos itens sujeitos ao ICMS", "status": "0", "correct": ""}, {"cod": "603", "msg": "Rejeição: Total do COFINS difere do somatório dos itens sujeitos ao ICMS", "status": "0", "correct": ""}, {"cod": "604", "msg": "Rejeição: Total do vOutro difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "605", "msg": "Rejeição: Total do vISS difere do somatório do vProd dos itens sujeitos ao ISSQN", "status": "0", "correct": ""}, {"cod": "606", "msg": "Rejeição: Total do vBC do ISS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "607", "msg": "Rejeição: Total do ISS difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "608", "msg": "Rejeição: Total do PIS difere do somatório dos itens sujeitos ao ISSQN", "status": "0", "correct": ""}, {"cod": "609", "msg": "Rejeição: Total do COFINS difere do somatório dos itens sujeitos ao ISSQN", "status": "0", "correct": ""}, {"cod": "610", "msg": "Rejeição: Total da NF difere do somatório dos Valores compõe o valor Total da NF.", "status": "0", "correct": ""}, {"cod": "611", "msg": "Rejeição: cEAN inválido", "status": "0", "correct": ""}, {"cod": "612", "msg": "Rejeição: cEANTrib inválido", "status": "0", "correct": ""}, {"cod": "613", "msg": "Rejeição: Chave de Acesso difere da existente em BD", "status": "0", "correct": ""}, {"cod": "614", "msg": "Rejeição: <PERSON><PERSON> de Acesso inválida (Código UF inválido)", "status": "0", "correct": ""}, {"cod": "615", "msg": "Rejeição: <PERSON><PERSON> inválida (<PERSON><PERSON> men<PERSON> que 06 ou Ano maior que Ano corrente)", "status": "0", "correct": ""}, {"cod": "616", "msg": "Rejeição: <PERSON><PERSON> inválid<PERSON> (<PERSON>ês menor que 1 ou <PERSON><PERSON>s maior que 12)", "status": "0", "correct": ""}, {"cod": "617", "msg": "Rejeição: <PERSON><PERSON>sso inválida (CNPJ zerado ou dígito inválido)", "status": "0", "correct": ""}, {"cod": "618", "msg": "Rejeição: <PERSON><PERSON>sso inválida (modelo diferente de 55 e 65)", "status": "0", "correct": ""}, {"cod": "619", "msg": "Rejeição: <PERSON><PERSON> Acesso inválida (número NF = 0)", "status": "0", "correct": ""}, {"cod": "620", "msg": "Rejeição: Chave de Acesso difere da existente em BD", "status": "0", "correct": ""}, {"cod": "621", "msg": "Rejeição: CPF Emitente não cadastrado", "status": "0", "correct": ""}, {"cod": "622", "msg": "Rejeição: IE emitente não vinculada ao CPF", "status": "0", "correct": ""}, {"cod": "623", "msg": "Rejeição: CPF Destinatário não cadastrado", "status": "0", "correct": ""}, {"cod": "624", "msg": "Rejeição: IE Destinatário não vinculada ao CPF", "status": "0", "correct": ""}, {"cod": "625", "msg": "Rejeição: Inscrição SUFRAMA deve ser informada na venda com isenção para ZFM", "status": "0", "correct": ""}, {"cod": "626", "msg": "Rejeição: CFOP de operação isenta para ZFM diferente do previsto", "status": "0", "correct": ""}, {"cod": "627", "msg": "Rejeição: O valor do ICMS desonerado deve ser informado", "status": "0", "correct": ""}, {"cod": "628", "msg": "Rejeição: Total da NF superior ao valor limite estabelecido pela SEFAZ [Limite]", "status": "0", "correct": ""}, {"cod": "629", "msg": "Rejeição: Valor do Produto difere do produto Valor Unitário de Comercialização e Quantidade Comercial", "status": "0", "correct": ""}, {"cod": "630", "msg": "Rejeição: Valor do Produto difere do produto Valor Unitário de Tributação e Quantidade Tributável", "status": "0", "correct": ""}, {"cod": "631", "msg": "Rejeição: CNPJ-Base do Destinatário difere do CNPJ-Base do Certificado Digital", "status": "0", "correct": ""}, {"cod": "632", "msg": "Rejeição: Solicitação fora de prazo, a NF-e não está mais disponível para download", "status": "0", "correct": ""}, {"cod": "633", "msg": "Rejeição: NF-e indisponível para download devido a ausência de Manifestação do Destinatário", "status": "0", "correct": ""}, {"cod": "634", "msg": "Rejeição: Destinatário da NF-e não tem o mesmo CNPJ raiz do solicitante do download", "status": "0", "correct": ""}, {"cod": "635", "msg": "Rejeição: NF-e com mesmo número e série já transmitida e aguardando processamento", "status": "0", "correct": ""}, {"cod": "650", "msg": "Rejeição: Evento de \"Ciência da Emissão\" para NF-e Cancelada ou Denegada", "status": "0", "correct": ""}, {"cod": "651", "msg": "Rejeição: Evento de \"Desconhecimento da Operação\" para NF-e Cancelada ou Denegada", "status": "0", "correct": ""}, {"cod": "653", "msg": "Rejeição: NF-e Cancelada, arquivo indisponível para download", "status": "0", "correct": ""}, {"cod": "654", "msg": "Rejeição: NF-e Denegada, arquivo indisponível para download", "status": "0", "correct": ""}, {"cod": "655", "msg": "Rejeição: Evento de Ciência da Emissão informado após a manifestação final do destinatário", "status": "0", "correct": ""}, {"cod": "656", "msg": "Rejeição: <PERSON><PERSON><PERSON>devid<PERSON>", "status": "0", "correct": ""}, {"cod": "657", "msg": "Rejeição: Código do Órgão diverge do órgão autorizador", "status": "0", "correct": ""}, {"cod": "658", "msg": "Rejeição: UF do destinatário da Chave de Acesso diverge da UF autorizadora", "status": "0", "correct": ""}, {"cod": "660", "msg": "Rejeição: CFOP de Combustível e não informado grupo de combustível da NF-e", "status": "0", "correct": ""}, {"cod": "661", "msg": "Rejeição: NF-e já existente para o número do EPEC informado", "status": "0", "correct": ""}, {"cod": "662", "msg": "Rejeição: Numeração do EPEC está inutilizada na Base de Dados da SEFAZ", "status": "0", "correct": ""}, {"cod": "663", "msg": "Rejeição: Alíquota do ICMS com valor superior a 4 por cento na operação de saída interestadual com produtos importados", "status": "0", "correct": ""}, {"cod": "678", "msg": "Rejeição: NF referenciada com UF diferente da NF-e complementar", "status": "0", "correct": ""}, {"cod": "679", "msg": "Rejeição: Modelo da NF-e referenciada diferente de 55/65", "status": "0", "correct": ""}, {"cod": "680", "msg": "Rejeição: Duplicidade de NF-e referenciada (Chave de Acesso referenciada mais de uma vez)", "status": "0", "correct": ""}, {"cod": "681", "msg": "Rejeição: Duplicidade de NF Modelo 1 referenciada (CNPJ, Modelo, Série e Número)", "status": "0", "correct": ""}, {"cod": "682", "msg": "Rejeição: Duplicidade de NF de Produtor referenciada (IE, Modelo, Série e Número)", "status": "0", "correct": ""}, {"cod": "683", "msg": "Rejeição: Modelo do CT-e referenciado diferente de 57", "status": "0", "correct": ""}, {"cod": "684", "msg": "Rejeição: Duplicidade de Cupom Fiscal referenciado (Modelo, Número de Ordem e COO)", "status": "0", "correct": ""}, {"cod": "685", "msg": "Rejeição: Total do Valor Aproximado dos Tributos difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "686", "msg": "Rejeição: NF Complementar referencia uma NF-e cancelada", "status": "0", "correct": ""}, {"cod": "687", "msg": "Rejeição: NF Complementar referencia uma NF-e denegada", "status": "0", "correct": ""}, {"cod": "688", "msg": "Rejeição: NF referenciada de Produtor com IE inexistente [nRef: xxx]", "status": "0", "correct": ""}, {"cod": "689", "msg": "Rejeição: NF referenciada de Produtor com IE não vinculada ao CNPJ/CPF informado [nRef: xxx]", "status": "0", "correct": ""}, {"cod": "690", "msg": "Rejeição: Pedido de Cancelamento para NF-e com CT-e", "status": "0", "correct": ""}, {"cod": "691", "msg": "Rejeição: <PERSON><PERSON> de Acesso da NF-e diverge da Chave de Acesso do EPEC", "status": "0", "correct": ""}, {"cod": "692", "msg": "692-Rejeição: Existe EPEC registrado para esta Série e Número [Chave EPEC: xxxxxxxxxxx]", "status": "0", "correct": ""}, {"cod": "693", "msg": "Alíquota de ICMS superior a definida para a operação interestadual [nItem:999]", "status": "0", "correct": ""}, {"cod": "694", "msg": "Peça orientação ao seu contador sobre a partilha de ICMS entre estados. No caso da operação ser com consumidor final (= 1 Sim), a UF do destinatário ser outra que a UF do emitente, e o tipo de contribuinte ser 9 - Não Contribuinte, que pode ou não possuir Inscrição Estadual no Cadastro de Contribuintes do ICMS, as informações de partilha de ICMS devem ser informadas no passo 3, como: pFCPUFDest, pICMSInter, pICMSInterPart, pICMSUFDest, vBCUFDest, vFCPUFDest, vICMSUFDest e vICMSUFRemet. Não informado o grupo de ICMS para a UF de destino [nItem:999]", "status": "0", "correct": ""}, {"cod": "695", "msg": " Informado indevidamente o grupo de ICMS para a UF de destino [nItem:999]", "status": "0", "correct": ""}, {"cod": "696", "msg": "No passo 1, mude \"Consumidor Final\" para \"1 - Sim\"Operacao com nao contribuinte deve indicar operacao com consumidor final", "status": "0", "correct": ""}, {"cod": "697", "msg": "Alíquota interestadual do ICMS com origem diferente do previsto [nItem:999]", "status": "0", "correct": ""}, {"cod": "698", "msg": "Peça orientação ao seu contador sobre a partilha de ICMS entre estados. No caso da operação ser com consumidor final (= 1 Sim), a UF do destinatário ser outra que a UF do emitente, e o tipo de contribuinte ser 9 - Não Contribuinte, que pode ou não possuir Inscrição Estadual no Cadastro de Contribuintes do ICMS, as informações de partilha de ICMS devem ser informadas no passo 3, como: pFCPUFDest, pICMSInter, pICMSInterPart, pICMSUFDest, vBCUFDest, vFCPUFDest, vICMSUFDest e vICMSUFRemet. Alíquota interestadual do ICMS incompatível com as UF envolvidas na operação [nItem:999]", "status": "0", "correct": ""}, {"cod": "699", "msg": "Percentual do ICMS Interestadual para a UF de destino difere do previsto para o ano da Data de Emissão [nItem:999]", "status": "0", "correct": ""}, {"cod": "700", "msg": "Rejeição: Mensagem de Lote versão 3.xx. Enviar para o Web Service nfeAutorizacao", "status": "0", "correct": ""}, {"cod": "701", "msg": "Rejeição: NF-e não pode utilizar a versão 3.00", "status": "0", "correct": ""}, {"cod": "702", "msg": "Rejeição: NFC-e não é aceita pela UF do Emitente", "status": "0", "correct": ""}, {"cod": "703", "msg": "Rejeição: Data-Hora de Emissão posterior ao horário de recebimento", "status": "0", "correct": ""}, {"cod": "704", "msg": "Rejeição: NFC-e com Data-Hora de emissão atrasada", "status": "0", "correct": ""}, {"cod": "705", "msg": "Rejeição: NFC-e com data de entrada/saída", "status": "0", "correct": ""}, {"cod": "706", "msg": "Rejeição: NFC-e para operação de entrada", "status": "0", "correct": ""}, {"cod": "707", "msg": "Rejeição: NFC-e para operação interestadual ou com o exterior", "status": "0", "correct": ""}, {"cod": "708", "msg": "Rejeição: NFC-e não pode referenciar documento fiscal", "status": "0", "correct": ""}, {"cod": "709", "msg": "Rejeição: NFC-e com formato de DANFE inválido", "status": "0", "correct": ""}, {"cod": "710", "msg": "Rejeição: NF-e com formato de DANFE inválido", "status": "0", "correct": ""}, {"cod": "711", "msg": "Rejeição: NF-e com contingência off-line", "status": "0", "correct": ""}, {"cod": "712", "msg": "Rejeição: NFC-e com contingência off-line para a UF", "status": "0", "correct": ""}, {"cod": "713", "msg": "Rejeição: Tipo de Emissão diferente de 6 ou 7 para contingência da SVC acessada", "status": "0", "correct": ""}, {"cod": "714", "msg": "Rejeição: NFC-e com contingência DPEC inexistente", "status": "0", "correct": ""}, {"cod": "715", "msg": "Rejeição: NFC-e com finalidade inválida", "status": "0", "correct": ""}, {"cod": "716", "msg": "Rejeição: NFC-e em operação não destinada a consumidor final", "status": "0", "correct": ""}, {"cod": "717", "msg": "Rejeição: NFC-e em operação não presencial", "status": "0", "correct": ""}, {"cod": "718", "msg": "Rejeição: NFC-e não deve informar IE de Substituto Tributário", "status": "0", "correct": ""}, {"cod": "719", "msg": "Rejeição: NF-e sem a identificação do destinatário", "status": "0", "correct": ""}, {"cod": "720", "msg": "Rejeição: Na operação com Exterior deve ser informada tag idEstrangeiro", "status": "0", "correct": ""}, {"cod": "721", "msg": "Rejeição: Operação interestadual deve informar CNPJ ou CPF.", "status": "0", "correct": ""}, {"cod": "722", "msg": "Operação interna com idEstrangeiro informado deve ser presencial", "status": "0", "correct": ""}, {"cod": "723", "msg": "Rejeição: Operação interna com idEstrangeiro informado deve ser para consumidor final", "status": "0", "correct": ""}, {"cod": "724", "msg": "Rejeição: NF-e sem o nome do destinatário", "status": "0", "correct": ""}, {"cod": "725", "msg": "Rejeição: NFC-e com CFOP inválido", "status": "0", "correct": ""}, {"cod": "726", "msg": "Rejeição: NF-e sem a informação de endereço do destinatário", "status": "0", "correct": ""}, {"cod": "727", "msg": "Rejeição: Operação com Exterior e UF diferente de EX", "status": "0", "correct": ""}, {"cod": "728", "msg": "Rejeição: NF-e sem informação da IE do destinatário", "status": "0", "correct": ""}, {"cod": "729", "msg": "Rejeição: NFC-e com informação da IE do destinatário", "status": "0", "correct": ""}, {"cod": "730", "msg": "Rejeição: NFC-e com Inscrição Suframa", "status": "0", "correct": ""}, {"cod": "731", "msg": "Rejeição: CFOP de operação com Exterior e idDest <> 3", "status": "0", "correct": ""}, {"cod": "732", "msg": "Rejeição: CFOP de operação interestadual e idDest <> 2", "status": "0", "correct": ""}, {"cod": "733", "msg": "Rejeição: CFOP de operação interna e idDest <> 1", "status": "0", "correct": ""}, {"cod": "734", "msg": "Rejeição: NFC-e com Unidade de Comercialização inválida", "status": "0", "correct": ""}, {"cod": "735", "msg": "Rejeição: NFC-e com Unidade de Tributação inválida", "status": "0", "correct": ""}, {"cod": "736", "msg": "Rejeição: NFC-e com grupo de Veículos novos", "status": "0", "correct": ""}, {"cod": "737", "msg": "Rejeição: NFC-e com grupo de Medicamentos", "status": "0", "correct": ""}, {"cod": "738", "msg": "Rejeição: NFC-e com grupo de Armamentos", "status": "0", "correct": ""}, {"cod": "739", "msg": "Rejeição: NFC-e com grupo de Combustível", "status": "0", "correct": ""}, {"cod": "740", "msg": "Rejeição: NFC-e com CST 51-Diferimento", "status": "0", "correct": ""}, {"cod": "741", "msg": "Rejeição: NFC-e com Partilha de ICMS entre UF", "status": "0", "correct": ""}, {"cod": "742", "msg": "Rejeição: NFC-e com grupo do IPI", "status": "0", "correct": ""}, {"cod": "743", "msg": "Rejeição: NFC-e com grupo do II", "status": "0", "correct": ""}, {"cod": "745", "msg": "Rejeição: NF-e sem grupo do PIS", "status": "0", "correct": ""}, {"cod": "746", "msg": "Rejeição: NFC-e com grupo do PIS-ST", "status": "0", "correct": ""}, {"cod": "748", "msg": "Rejeição: NF-e sem grupo da COFINS", "status": "0", "correct": ""}, {"cod": "749", "msg": "Rejeição: NFC-e com grupo da COFINS-ST", "status": "0", "correct": ""}, {"cod": "750", "msg": "Rejeição: NFC-e com valor total superior ao permitido para destinatário não identificado (Código)", "status": "0", "correct": ""}, {"cod": "751", "msg": "Rejeição: NFC-e com valor total superior ao permitido para destinatário não identificado (Nome)", "status": "0", "correct": ""}, {"cod": "752", "msg": "Rejeição: NFC-e com valor total superior ao permitido para destinatário não identificado (Endereço)", "status": "0", "correct": ""}, {"cod": "753", "msg": "Rejeição: NFC-e com Frete", "status": "0", "correct": ""}, {"cod": "754", "msg": "Rejeição: NFC-e com dados do Transportador", "status": "0", "correct": ""}, {"cod": "755", "msg": "Rejeição: NFC-e com dados de Retenção do ICMS no Transporte", "status": "0", "correct": ""}, {"cod": "756", "msg": "Rejeição: NFC-e com dados do veículo de Transporte", "status": "0", "correct": ""}, {"cod": "757", "msg": "Rejeição: NFC-e com dados de Reboque do veículo de Transporte", "status": "0", "correct": ""}, {"cod": "758", "msg": "Rejeição: NFC-e com dados do Vagão de Transporte", "status": "0", "correct": ""}, {"cod": "759", "msg": "Rejeição: NFC-e com dados da Balsa de Transporte", "status": "0", "correct": ""}, {"cod": "760", "msg": "Rejeição: NFC-e com dados de cobrança (Fatura, Duplicata)", "status": "0", "correct": ""}, {"cod": "762", "msg": "Rejeição: NFC-e com dados de compras (Empenho, Pedido, Contrato)", "status": "0", "correct": ""}, {"cod": "763", "msg": "Rejeição: NFC-e com dados de aquisição de Cana", "status": "0", "correct": ""}, {"cod": "764", "msg": "Rejeição: Solicitada resposta síncrona para Lote com mais de uma NF-e (indSinc=1)", "status": "0", "correct": ""}, {"cod": "765", "msg": "Rejeição: Lote só poderá conter NF-e ou NFC-e", "status": "0", "correct": ""}, {"cod": "766", "msg": "Rejeição: NFC-e com CST 50-Suspensão", "status": "0", "correct": ""}, {"cod": "767", "msg": "Rejeição: NFC-e com somatório dos pagamentos diferente do total da Nota Fiscal", "status": "0", "correct": ""}, {"cod": "768", "msg": "Rejeição: NF-e não deve possuir o grupo de Formas de Pagamento", "status": "0", "correct": ""}, {"cod": "769", "msg": "Rejeição: A critério da UF NFC-e deve possuir o grupo de Formas de Pagamento", "status": "0", "correct": ""}, {"cod": "770", "msg": "Rejeição: NFC-e autorizada há mais de 24 horas.", "status": "0", "correct": ""}, {"cod": "771", "msg": "Rejeição: Operação Interestadual e UF de destino com EX", "status": "0", "correct": ""}, {"cod": "772", "msg": "Rejeição: Operação Interestadual e UF de destino igual à UF do emitente", "status": "0", "correct": "Operação indicada como interestadual mas a UF do destinatário é a mesmo do emitente."}, {"cod": "773", "msg": "Rejeição: Operação Interna e UF de destino difere da UF do emitente", "status": "0", "correct": "A UF do destinatário é diferente do emitente"}, {"cod": "774", "msg": "Rejeição: NFC-e com indicador de item não participante do total", "status": "0", "correct": "Em NFCe todos os itens da notas integram o total, algum item não está dessa forma."}, {"cod": "775", "msg": "Rejeição: Modelo da NFC-e diferente de 65", "status": "0", "correct": "NFCe deve ter modelo 65 apenas, , informe o administrador do sistema"}, {"cod": "776", "msg": "Rejeição: Solicitada resposta síncrona para UF que não disponibiliza este atendimento (indSinc=1)", "status": "0", "correct": "Não existe opereção sincrona, informe o administrador do sistema"}, {"cod": "777", "msg": "Rejeição: Obrigatória a informação do NCM completo", "status": "0", "correct": "NCM incompleto, esse numero deve ter 8 digitos"}, {"cod": "778", "msg": "Rejeição: Informado NCM inexistente", "status": "0", "correct": "Verifique o código NCM está errado. Lembre-se que esses códigos mudam e ninguém te avisa disso"}, {"cod": "779", "msg": "Rejeição: NFC-e com NCM incompatível", "status": "0", "correct": "NCM do produto incompatível com NFCe"}, {"cod": "780", "msg": "Rejeição: Total da NFC-e superior ao valor limite estabelecido pela SEFAZ [Limite]", "status": "0", "correct": "A NFCe indica um valor muito alto, acima do permitido"}, {"cod": "781", "msg": "Rejeição: Emissor não habilitado para emissão da NFC-e", "status": "0", "correct": "CNPJ não habilitado, entre em contado com SEFAZ"}, {"cod": "782", "msg": "Rejeição: NFC-e não é autorizada pelo SCAN", "status": "0", "correct": "NFCe não pode usar essa contingêcia"}, {"cod": "783", "msg": "Rejeição: NFC-e não é autorizada pela SVC", "status": "0", "correct": "NFCe não pode usar essa contingêcia"}, {"cod": "784", "msg": "Rejeição: NFC-e não permite o evento de Carta de Correção", "status": "0", "correct": "NFCe não aceita carta de correção, se existe erro cancele e emita outra NFCe"}, {"cod": "785", "msg": "Rejeição: NFC-e com entrega a domicílio não permitida pela UF", "status": "0", "correct": "Esta UF não permite essa operação com entrega em domicilio"}, {"cod": "786", "msg": "Rejeição: NFC-e de entrega a domicílio sem dados do Transportador", "status": "0", "correct": "Entrega em domicilio sem dados do transportador"}, {"cod": "787", "msg": "Rejeição: NFC-e de entrega a domicílio sem a identificação do destinatário", "status": "0", "correct": "Entrega em domicilio sem a identificação do destinatário"}, {"cod": "788", "msg": "Rejeição: NFC-e de entrega a domicílio sem o endereço do destinatário", "status": "0", "correct": "Indicada entrega em domicilio sem o endereço do destinatário"}, {"cod": "789", "msg": "Rejeição: NFC-e para destinatário contribuinte de ICMS", "status": "0", "correct": "NFCe é apenas para consumidores e não para contribuintes"}, {"cod": "790", "msg": "Rejeição: Operação com Exterior para destinatário Contribuinte de ICMS", "status": "0", "correct": "Exportação não pode ter um contribuinte do ICMS no exterior"}, {"cod": "791", "msg": "Rejeição: NF-e com indicação de destinatário isento de IE, com a informação da IE do destinatário", "status": "0", "correct": "Foi indicada operação com Contribuinte isento de Inscrição no cadastro de Contribuintes do ICMS e informada a IE do destinatário"}, {"cod": "792", "msg": "Rejeição: Informada a IE do destinatário para operação com destinatário no Exterior", "status": "0", "correct": "Em operações de exportação não pode ser indicado um IE"}, {"cod": "793", "msg": "Rejeição: Informado Capítulo do NCM inexistente", "status": "0", "correct": "Confira o NCM está incorreto"}, {"cod": "794", "msg": "Rejeição: NF-e com indicativo de NFC-e com entrega a domicílio", "status": "0", "correct": "NFC-e em operação com entrega a domicílio e não foram informados os dados do destinatário"}, {"cod": "795", "msg": "Rejeição: Total do ICMS desonerado difere do somatório dos itens", "status": "0", "correct": "Valor da soma do ICMS desonerado dos itens não está igual ao valor do ICMS desonerado no total da nota. Verifique se o CST da nota está correto, caso esteja como por exemplo '00 - Tributado Integralmente' o valor do ICMS desonerado por item não será destacado, gerado assim essa rejeição no envio."}, {"cod": "796", "msg": "Rejeição: Empresa sem Chave de Segurança para o QR-Code", "status": "0", "correct": "Confira o CSC e o CSCid"}, {"cod": "798", "msg": "Valor total do ICMS relativo Fundo de Combate à Pobreza (FCP) da UF de destino difere do somatório do valor dos itens", "status": "0", "correct": ""}, {"cod": "799", "msg": "Valor total do ICMS Interestadual da UF de destino difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "800", "msg": "Valor total do ICMS Interestadual da UF do remetente difere do somatório dos itens", "status": "0", "correct": ""}, {"cod": "805", "msg": "A SEFAZ do destinatário não permite Contribuinte Isento de Inscrição Estadual", "status": "0", "correct": ""}, {"cod": "806", "msg": "A partir de 01.10.2016 é obrigatória a informação do CEST, Código Especificador de Substituição Tributária, um código composto por 6 algarimos. No painel editar o produto você tem um botão \"Tabela CEST\" que exibe uma lista completa dos CESTs.Operação com ICMS-ST sem informação do CEST", "status": "0", "correct": ""}, {"cod": "807", "msg": "NFC-e com grupo de ICMS para a UF do destinatário", "status": "0", "correct": ""}, {"cod": "817", "msg": "Unidade Tributavel incompativel com o NCM informado na operacao com Comercio Exterior", "status": "0", "correct": ""}, {"cod": "999", "msg": "Rejeição: Erro não catalogado (informar a mensagem de erro capturado no tratamento da exceção)", "status": "0", "correct": "Isso é um problema provavelmente da SEFAZ"}, {"cod": "300", "msg": "Tipo da IE do Destinatário difere de Não Contribuinte", "status": "0", "correct": "Verificar o tipo de IE do destinatário"}, {"cod": "444", "msg": "NFCe com qrCode versão 2 para Pessoa Física", "status": "0", "correct": "Atualizar para qrCode versão 3 para pessoa física"}, {"cod": "445", "msg": "Parâmetro assinatura do qrCode inválido", "status": "0", "correct": "Verificar a assinatura do qrCode versão 3"}, {"cod": "452", "msg": "Resposta assíncrona para lote com apenas 1 NFe", "status": "0", "correct": "Usar resposta síncrona para lotes unitários"}, {"cod": "474", "msg": "Validação do parâmetro assinatura no qrCode", "status": "0", "correct": "Verificar parâmetros da assinatura do qrCode"}, {"cod": "583", "msg": "Valor da assinatura do qrCode difere do calculado", "status": "0", "correct": "Recalcular a assinatura do qrCode"}, {"cod": "797", "msg": "Validação específica NT 2025.001", "status": "0", "correct": "Verificar conformidade com NT 2025.001"}, {"cod": "805", "msg": "Erro na validação de dados conforme NT 2025.001", "status": "0", "correct": "Revisar dados conforme especificações atualizadas"}, {"cod": "853", "msg": "Validação de cobrança e pagamento NT 2025.001", "status": "0", "correct": "Verificar dados de cobrança e pagamento"}, {"cod": "", "msg": null, "status": null, "correct": null}]