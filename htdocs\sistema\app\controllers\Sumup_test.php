<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Controlador de Teste SumUp (sem autenticação)
 */
class Sumup_test extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        
        // Não verificar login para testes
        $this->load->model('sumup_model');
    }

    /**
     * Teste básico
     */
    public function index()
    {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Controlador SumUp Test funcionando!',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Testar configuração SumUp
     */
    public function test_config()
    {
        header('Content-Type: application/json');
        
        try {
            $configured = $this->sumup_model->isConfigured();
            
            echo json_encode([
                'success' => true,
                'configured' => $configured,
                'message' => $configured ? 'SumUp está configurado' : 'SumUp não está configurado'
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Listar dispositivos SumUp
     */
    public function list_devices()
    {
        header('Content-Type: application/json');
        
        try {
            if (!$this->sumup_model->isConfigured()) {
                echo json_encode(['error' => true, 'message' => 'SumUp não está configurado']);
                return;
            }
            
            $result = $this->sumup_model->listCardReaders();
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'devices' => $result['data'],
                    'count' => is_array($result['data']) ? count($result['data']) : 0
                ]);
            } else {
                echo json_encode([
                    'error' => true,
                    'message' => 'Erro ao listar dispositivos: ' . ($result['data']['message'] ?? 'Erro desconhecido'),
                    'details' => $result['data']
                ]);
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'error' => true,
                'message' => 'Exceção: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Testar criação de pagamento na máquina
     */
    public function test_card_payment()
    {
        header('Content-Type: application/json');
        
        try {
            if (!$this->sumup_model->isConfigured()) {
                echo json_encode(['error' => true, 'message' => 'SumUp não está configurado']);
                return;
            }
            
            $amount = $this->input->get('amount') ?: '5.00';
            $description = $this->input->get('description') ?: 'Teste Pagamento Máquina';
            
            $result = $this->sumup_model->createCardReaderPayment($amount, 'BRL', $description);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'transaction_id' => $result['data']['id'] ?? null,
                    'message' => 'Pagamento enviado para a máquina SumUp!',
                    'amount' => $amount,
                    'data' => $result['data']
                ]);
            } else {
                echo json_encode([
                    'error' => true,
                    'message' => 'Erro ao criar pagamento: ' . ($result['data']['message'] ?? 'Erro desconhecido'),
                    'details' => $result['data']
                ]);
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'error' => true,
                'message' => 'Exceção: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Testar conexão com API
     */
    public function test_connection()
    {
        header('Content-Type: application/json');

        try {
            if (!$this->sumup_model->isConfigured()) {
                echo json_encode(['error' => true, 'message' => 'SumUp não está configurado']);
                return;
            }

            $result = $this->sumup_model->testConnection();

            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Conexão com SumUp OK!',
                    'merchant_data' => $result['data']
                ]);
            } else {
                echo json_encode([
                    'error' => true,
                    'message' => 'Erro de conexão: ' . ($result['data']['message'] ?? 'Erro desconhecido'),
                    'details' => $result['data']
                ]);
            }

        } catch (Exception $e) {
            echo json_encode([
                'error' => true,
                'message' => 'Exceção: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Testar vários endpoints para encontrar o correto
     */
    public function test_endpoints()
    {
        header('Content-Type: application/json');

        try {
            if (!$this->sumup_model->isConfigured()) {
                echo json_encode(['error' => true, 'message' => 'SumUp não está configurado']);
                return;
            }

            $result = $this->sumup_model->testEndpoints();

            echo json_encode([
                'success' => true,
                'message' => 'Teste de endpoints concluído',
                'results' => $result['data']
            ]);

        } catch (Exception $e) {
            echo json_encode([
                'error' => true,
                'message' => 'Exceção: ' . $e->getMessage()
            ]);
        }
    }
}
?>
