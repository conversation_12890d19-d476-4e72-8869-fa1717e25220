<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Sumup_webhook extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('sumup_model');
        $this->load->model('pos_model');
        $this->load->model('sales_model');
    }

    /**
     * Endpoint principal para receber webhooks da SumUp
     */
    public function index()
    {
        // Obter payload do webhook
        $payload = file_get_contents('php://input');
        $signature = $this->input->get_request_header('X-Sumup-Signature');

        // Log do webhook recebido
        log_message('info', 'SumUp Webhook received: ' . $payload);

        // Validar webhook se secret estiver configurado
        if (!$this->sumup_model->validateWebhook($payload, $signature)) {
            log_message('error', 'SumUp Webhook signature validation failed');
            http_response_code(401);
            echo 'Unauthorized';
            return;
        }

        // Processar webhook
        $result = $this->sumup_model->processWebhook($payload);

        if ($result['success']) {
            $webhook_data = $result['data'];
            
            // Processar diferentes tipos de eventos
            if (isset($webhook_data['event_type'])) {
                switch ($webhook_data['event_type']) {
                    case 'CHECKOUT_PAID':
                        $this->_process_checkout_paid($webhook_data);
                        break;
                    case 'CHECKOUT_FAILED':
                        $this->_process_checkout_failed($webhook_data);
                        break;
                    case 'TRANSACTION_SUCCESSFUL':
                        $this->_process_transaction_successful($webhook_data);
                        break;
                    case 'TRANSACTION_FAILED':
                        $this->_process_transaction_failed($webhook_data);
                        break;
                    default:
                        log_message('info', 'SumUp webhook event not handled: ' . $webhook_data['event_type']);
                }
            } else {
                // Webhook sem event_type - processar baseado no status do checkout
                if (isset($webhook_data['status'])) {
                    switch ($webhook_data['status']) {
                        case 'PAID':
                            $this->_process_checkout_paid($webhook_data);
                            break;
                        case 'FAILED':
                            $this->_process_checkout_failed($webhook_data);
                            break;
                    }
                }
            }

            http_response_code(200);
            echo 'OK';
        } else {
            log_message('error', 'SumUp Webhook processing failed: ' . $result['message']);
            http_response_code(400);
            echo 'Bad Request';
        }
    }

    /**
     * Processar checkout pago
     */
    private function _process_checkout_paid($webhook_data)
    {
        try {
            $checkout_id = $webhook_data['id'] ?? null;
            $checkout_reference = $webhook_data['checkout_reference'] ?? null;
            $amount = $webhook_data['amount'] ?? 0;
            $transaction_id = null;

            // Obter ID da transação se disponível
            if (isset($webhook_data['transactions']) && is_array($webhook_data['transactions'])) {
                foreach ($webhook_data['transactions'] as $transaction) {
                    if ($transaction['status'] === 'SUCCESSFUL') {
                        $transaction_id = $transaction['id'];
                        break;
                    }
                }
            }

            log_message('info', "SumUp checkout paid: {$checkout_id}, reference: {$checkout_reference}, amount: {$amount}");

            // Aqui você pode implementar a lógica para:
            // 1. Encontrar a venda relacionada ao checkout_reference
            // 2. Atualizar o status da venda
            // 3. Registrar o pagamento na tabela tec_payments
            // 4. Enviar notificação para o usuário

            // Exemplo de como registrar o pagamento:
            if ($checkout_reference && $amount > 0) {
                $this->_register_sumup_payment($checkout_reference, $amount, $transaction_id, $checkout_id);
            }

        } catch (Exception $e) {
            log_message('error', 'Error processing SumUp checkout paid: ' . $e->getMessage());
        }
    }

    /**
     * Processar checkout falhado
     */
    private function _process_checkout_failed($webhook_data)
    {
        try {
            $checkout_id = $webhook_data['id'] ?? null;
            $checkout_reference = $webhook_data['checkout_reference'] ?? null;

            log_message('info', "SumUp checkout failed: {$checkout_id}, reference: {$checkout_reference}");

            // Implementar lógica para lidar com falha de pagamento
            // Por exemplo, notificar o usuário, cancelar a venda, etc.

        } catch (Exception $e) {
            log_message('error', 'Error processing SumUp checkout failed: ' . $e->getMessage());
        }
    }

    /**
     * Processar transação bem-sucedida
     */
    private function _process_transaction_successful($webhook_data)
    {
        try {
            $transaction_id = $webhook_data['id'] ?? null;
            $amount = $webhook_data['amount'] ?? 0;

            log_message('info', "SumUp transaction successful: {$transaction_id}, amount: {$amount}");

            // Implementar lógica específica para transação bem-sucedida

        } catch (Exception $e) {
            log_message('error', 'Error processing SumUp transaction successful: ' . $e->getMessage());
        }
    }

    /**
     * Processar transação falhada
     */
    private function _process_transaction_failed($webhook_data)
    {
        try {
            $transaction_id = $webhook_data['id'] ?? null;

            log_message('info', "SumUp transaction failed: {$transaction_id}");

            // Implementar lógica específica para transação falhada

        } catch (Exception $e) {
            log_message('error', 'Error processing SumUp transaction failed: ' . $e->getMessage());
        }
    }

    /**
     * Registrar pagamento SumUp no sistema
     */
    private function _register_sumup_payment($checkout_reference, $amount, $transaction_id = null, $checkout_id = null)
    {
        try {
            // Esta é uma implementação básica
            // Você precisará adaptar conforme sua lógica de negócio

            // Exemplo: se o checkout_reference contém o ID da venda
            if (strpos($checkout_reference, 'pdv_') === 0) {
                // Extrair informações do checkout_reference se necessário
                // Por exemplo: pdv_sale_123 -> sale_id = 123
                
                $payment_data = [
                    'date' => date('Y-m-d H:i:s'),
                    'amount' => $amount,
                    'paid_by' => 'sumup',
                    'transaction_id' => $transaction_id,
                    'note' => "Pagamento SumUp - Checkout: {$checkout_id}",
                    'created_by' => 1 // ID do usuário do sistema, ajustar conforme necessário
                ];

                // Se você tiver o sale_id, pode registrar o pagamento diretamente
                // $payment_data['sale_id'] = $sale_id;
                // $this->sales_model->addPayment($payment_data);

                log_message('info', 'SumUp payment registered: ' . json_encode($payment_data));
            }

        } catch (Exception $e) {
            log_message('error', 'Error registering SumUp payment: ' . $e->getMessage());
        }
    }

    /**
     * Endpoint para testar webhooks (apenas para desenvolvimento)
     */
    public function test()
    {
        if (ENVIRONMENT !== 'development') {
            show_404();
            return;
        }

        // Simular webhook de checkout pago
        $test_payload = [
            'id' => 'test-checkout-id',
            'checkout_reference' => 'pdv_test_' . time(),
            'amount' => 10.50,
            'status' => 'PAID',
            'event_type' => 'CHECKOUT_PAID',
            'transactions' => [
                [
                    'id' => 'test-transaction-id',
                    'status' => 'SUCCESSFUL',
                    'amount' => 10.50
                ]
            ]
        ];

        echo '<h2>Testando Webhook SumUp</h2>';
        echo '<p>Payload de teste:</p>';
        echo '<pre>' . json_encode($test_payload, JSON_PRETTY_PRINT) . '</pre>';

        $this->_process_checkout_paid($test_payload);

        echo '<p>Webhook processado com sucesso!</p>';
    }
}
