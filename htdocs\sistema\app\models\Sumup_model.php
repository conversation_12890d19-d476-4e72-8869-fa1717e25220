<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Sumup_model extends CI_Model
{
    private $api_key;
    private $client_id;
    private $client_secret;
    private $merchant_code;
    private $pay_to_email;
    private $api_url;
    private $sandbox;
    private $webhook_secret;
    
    public function __construct()
    {
        parent::__construct();
        
        // Carregar configurações do config.php
        global $SUMUP_API_KEY, $SUMUP_CLIENT_ID, $SUMUP_CLIENT_SECRET,
               $SUMUP_MERCHANT_CODE, $SUMUP_PAY_TO_EMAIL, $SUMUP_API_URL, $SUMUP_SANDBOX, $SUMUP_WEBHOOK_SECRET;

        $this->api_key = $SUMUP_API_KEY;
        $this->client_id = $SUMUP_CLIENT_ID;
        $this->client_secret = $SUMUP_CLIENT_SECRET;
        $this->merchant_code = $SUMUP_MERCHANT_CODE;
        $this->pay_to_email = $SUMUP_PAY_TO_EMAIL;
        $this->api_url = $SUMUP_API_URL;
        $this->sandbox = $SUMUP_SANDBOX;
        $this->webhook_secret = $SUMUP_WEBHOOK_SECRET;
    }
    
    /**
     * Obter token de acesso OAuth
     */
    private function getAccessToken()
    {
        // Se já temos um token válido, usar ele
        if (!empty($this->api_key) && strpos($this->api_key, 'sup_sk_') === 0) {
            return $this->api_key;
        }

        // Se não temos client_id e client_secret, não podemos obter token
        if (empty($this->client_id) || empty($this->client_secret)) {
            return null;
        }

        // Fazer requisição OAuth para obter token
        $auth_url = $this->api_url . '/token';
        $auth_data = [
            'grant_type' => 'client_credentials',
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'scope' => 'payments'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $auth_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($auth_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code === 200) {
            $token_data = json_decode($response, true);
            if (isset($token_data['access_token'])) {
                return $token_data['access_token'];
            }
        }

        return null;
    }

    /**
     * Fazer requisição HTTP para a API SumUp
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null, $headers = [])
    {
        $url = $this->api_url . $endpoint;

        // Log da requisição
        log_message('debug', 'SumUp makeRequest: ' . $method . ' ' . $url);
        if ($data) {
            log_message('debug', 'SumUp request data: ' . json_encode($data));
        }

        // Obter token de acesso
        $access_token = $this->getAccessToken();
        if (!$access_token) {
            log_message('error', 'SumUp: Não foi possível obter token de acesso');
            return ['error' => true, 'message' => 'Não foi possível obter token de acesso'];
        }

        $default_headers = [
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $headers = array_merge($default_headers, $headers);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            log_message('error', 'SumUp cURL Error: ' . $error);
            return ['error' => true, 'message' => 'cURL Error: ' . $error];
        }

        // Log da resposta
        log_message('debug', 'SumUp response HTTP ' . $http_code . ': ' . $response);

        $decoded_response = json_decode($response, true);

        $result = [
            'success' => $http_code >= 200 && $http_code < 300,
            'http_code' => $http_code,
            'data' => $decoded_response,
            'raw_response' => $response
        ];

        if (!$result['success']) {
            log_message('error', 'SumUp API Error: HTTP ' . $http_code . ' - ' . $response);
        }

        return $result;
    }
    
    /**
     * Criar um checkout SumUp (API oficial)
     */
    public function createCheckout($amount, $currency = 'BRL', $description = null, $checkout_reference = null, $options = [])
    {
        // Gerar referência única mais robusta
        if (!$checkout_reference) {
            $checkout_reference = 'PDV_' . date('YmdHis') . '_' . uniqid() . '_' . mt_rand(1000, 9999);
        }

        $data = [
            'checkout_reference' => $checkout_reference,
            'amount' => floatval($amount),
            'currency' => $currency,
            'description' => $description ?: 'Pagamento PDV',
            'hosted_checkout' => [
                'enabled' => true
            ]
        ];

        // Adicionar redirect_url se fornecido (para 3DS)
        if (!empty($options['redirect_url'])) {
            $data['redirect_url'] = $options['redirect_url'];
        }

        // Adicionar customer_id se fornecido
        if (!empty($options['customer_id'])) {
            $data['customer_id'] = $options['customer_id'];
        }

        // Adicionar merchant_code OU pay_to_email
        if (!empty($this->merchant_code)) {
            $data['merchant_code'] = $this->merchant_code;
        } elseif (!empty($this->pay_to_email)) {
            $data['pay_to_email'] = $this->pay_to_email;
        }
        
        // Log para debug
        log_message('debug', 'SumUp createCheckout data: ' . json_encode($data));

        $result = $this->makeRequest('/v0.1/checkouts', 'POST', $data);

        // Log do resultado
        log_message('debug', 'SumUp createCheckout result: ' . json_encode($result));

        return $result;
    }
    
    /**
     * Recuperar informações de um checkout
     */
    public function getCheckout($checkout_id)
    {
        return $this->makeRequest('/v0.1/checkouts/' . $checkout_id);
    }
    
    /**
     * Processar um checkout (finalizar pagamento)
     */
    public function processCheckout($checkout_id, $payment_data)
    {
        return $this->makeRequest('/v0.1/checkouts/' . $checkout_id, 'PUT', $payment_data);
    }
    
    /**
     * Listar checkouts
     */
    public function listCheckouts($checkout_reference = null)
    {
        $endpoint = '/v0.1/checkouts';
        if ($checkout_reference) {
            $endpoint .= '?checkout_reference=' . urlencode($checkout_reference);
        }
        
        return $this->makeRequest($endpoint);
    }
    
    /**
     * Desativar um checkout
     */
    public function deactivateCheckout($checkout_id)
    {
        return $this->makeRequest('/v0.1/checkouts/' . $checkout_id, 'DELETE');
    }
    
    /**
     * Recuperar informações de uma transação
     */
    public function getTransaction($transaction_id)
    {
        return $this->makeRequest('/v0.1/me/transactions/' . $transaction_id);
    }
    
    /**
     * Listar transações
     */
    public function listTransactions($limit = 10, $order = 'descending')
    {
        $endpoint = '/v0.1/me/transactions?limit=' . $limit . '&order=' . $order;
        return $this->makeRequest($endpoint);
    }
    
    /**
     * Criar um reembolso
     */
    public function createRefund($transaction_id, $amount = null)
    {
        $data = [];
        if ($amount) {
            $data['amount'] = floatval($amount);
        }
        
        return $this->makeRequest('/v0.1/me/refund/' . $transaction_id, 'POST', $data);
    }
    
    /**
     * Validar webhook da SumUp
     */
    public function validateWebhook($payload, $signature)
    {
        if (!$this->webhook_secret) {
            return true; // Se não há secret configurado, aceita qualquer webhook
        }
        
        $expected_signature = hash_hmac('sha256', $payload, $this->webhook_secret);
        return hash_equals($expected_signature, $signature);
    }
    
    /**
     * Processar webhook da SumUp
     */
    public function processWebhook($payload)
    {
        $data = json_decode($payload, true);
        
        if (!$data) {
            return ['error' => true, 'message' => 'Invalid JSON payload'];
        }
        
        // Log do webhook para debug
        log_message('info', 'SumUp Webhook received: ' . $payload);
        
        return ['success' => true, 'data' => $data];
    }
    
    /**
     * Obter URL do checkout para pagamento
     */
    public function getCheckoutUrl($checkout_id)
    {
        // URL para o usuário fazer o pagamento
        // Baseado na documentação oficial: https://developer.sumup.com/online-payments/tools/hosted-checkout/
        // Formato: https://checkout.sumup.com/pay/{checkout_id}
        return "https://checkout.sumup.com/pay/{$checkout_id}";
    }
    
    /**
     * Verificar se as credenciais estão configuradas
     */
    public function isConfigured()
    {
        // Verificar se temos merchant_code OU pay_to_email
        if (empty($this->merchant_code) && empty($this->pay_to_email)) {
            return false;
        }

        // Verificar se temos chave privada OU credenciais OAuth
        $has_private_key = !empty($this->api_key) && strpos($this->api_key, 'sup_sk_') === 0;
        $has_oauth_creds = !empty($this->client_id) && !empty($this->client_secret);

        return $has_private_key || $has_oauth_creds;
    }
    
    /**
     * Testar conexão com a API
     */
    public function testConnection()
    {
        $response = $this->makeRequest('/v0.1/me');
        return $response;
    }

    /**
     * Testar diferentes endpoints para encontrar o correto
     */
    public function testEndpoints()
    {
        $endpoints_to_test = [
            '/v0.1/me',
            '/v0.1/readers',
            '/v0.1/me/readers',
            '/v0.1/devices',
            '/v0.1/me/devices',
            '/v0.1/card-readers',
            '/v0.1/me/card-readers',
            '/v0.1/transactions',
            '/v0.1/me/transactions',
            '/v0.1/checkouts'
        ];

        $results = [];

        foreach ($endpoints_to_test as $endpoint) {
            log_message('debug', 'Testing SumUp endpoint: ' . $endpoint);
            $response = $this->makeRequest($endpoint);
            $results[$endpoint] = [
                'success' => $response['success'],
                'http_code' => $response['http_code'] ?? null,
                'error' => $response['success'] ? null : ($response['data']['detail'] ?? 'Unknown error'),
                'data_preview' => $response['success'] ? (is_array($response['data']) ? count($response['data']) . ' items' : 'object') : null
            ];
        }

        return [
            'success' => true,
            'data' => $results
        ];
    }

    /**
     * Listar máquinas (card readers) disponíveis
     */
    public function listCardReaders()
    {
        $response = $this->makeRequest('/v0.1/readers');
        return $response;
    }

    /**
     * Criar pagamento usando checkout web (única opção disponível via API REST)
     */
    public function createCardReaderPayment($amount, $currency = 'BRL', $description = null, $reader_id = null)
    {
        // IMPORTANTE: A SumUp não oferece API REST pública para controle direto de card readers
        // A única opção via API REST é o checkout web

        log_message('info', 'SumUp: API REST não suporta controle direto de card readers. Usando checkout web.');

        return [
            'success' => false,
            'message' => 'API REST da SumUp não suporta controle direto de máquinas físicas.',
            'error_code' => 'CARD_READER_API_NOT_AVAILABLE',
            'alternatives' => [
                'checkout_web' => 'Use o checkout web para pagamentos online',
                'native_sdk' => 'Use o SDK nativo para iOS/Android para controle de máquinas físicas',
                'deep_link' => 'Use deep links em dispositivos móveis com app SumUp'
            ],
            'recommendation' => 'Para integração com SumUp Solo, use um dispositivo móvel com app SumUp e deep links'
        ];
    }

    /**
     * Gerar deep link para app SumUp (funciona apenas em dispositivos móveis)
     */
    public function generateDeepLink($amount, $currency = 'BRL', $description = null)
    {
        $amount_formatted = number_format(floatval($amount), 2, '.', '');

        // Deep link oficial da SumUp para pagamentos
        $deep_link_params = [
            'amount' => $amount_formatted,
            'currency' => $currency,
            'affiliate-key' => 'sup_afk_xSf3YzyoRxriFGoZFrutoUyrtT9f52OS', // Sua chave de afiliado
            'title' => $description ?: 'Pagamento PDV'
        ];

        $deep_link = 'sumupmerchant://pay/1.0?' . http_build_query($deep_link_params);

        log_message('debug', 'SumUp deep link gerado: ' . $deep_link);

        return [
            'success' => true,
            'data' => [
                'deep_link' => $deep_link,
                'qr_code_data' => $deep_link,
                'instructions' => [
                    '1. Instale o app SumUp no celular/tablet',
                    '2. Conecte sua SumUp Solo via Bluetooth no app',
                    '3. Clique no link ou escaneie o QR Code',
                    '4. O valor aparecerá na máquina para pagamento'
                ],
                'requirements' => [
                    'App SumUp instalado',
                    'SumUp Solo conectada via Bluetooth',
                    'Dispositivo móvel (iOS/Android)'
                ]
            ]
        ];
    }
}
