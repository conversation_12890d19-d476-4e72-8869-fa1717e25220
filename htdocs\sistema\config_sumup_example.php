<?php
/**
 * Exemplo de Configuração SumUp
 * 
 * Copie estas configurações para o seu arquivo config.php
 * e preencha com suas credenciais reais da SumUp
 */

// CONFIGURAÇÕES DA API SUMUP
$SUMUP_API_KEY = ""; // Sua chave da API SumUp (obtenha em https://developer.sumup.com/)
$SUMUP_CLIENT_ID = ""; // Client ID da sua aplicação SumUp
$SUMUP_CLIENT_SECRET = ""; // Client Secret da sua aplicação SumUp
$SUMUP_MERCHANT_CODE = ""; // Código do merchant SumUp
$SUMUP_SANDBOX = true; // true para sandbox, false para produção
$SUMUP_API_URL = $SUMUP_SANDBOX ? "https://api.sumup.com" : "https://api.sumup.com";
$SUMUP_WEBHOOK_SECRET = ""; // Secret para validar webhooks (opcional)

/**
 * COMO OBTER AS CREDENCIAIS:
 * 
 * 1. Acesse: https://developer.sumup.com/
 * 2. Faça login com sua conta SumUp
 * 3. Crie uma nova aplicação
 * 4. Copie as credenciais geradas
 * 
 * SANDBOX vs PRODUÇÃO:
 * 
 * - Sandbox: Para testes, use $SUMUP_SANDBOX = true
 * - Produção: Para uso real, use $SUMUP_SANDBOX = false
 * 
 * WEBHOOK (Opcional):
 * 
 * - Configure no painel SumUp: https://seudominio.com/sistema/sumup_webhook
 * - Eventos recomendados: CHECKOUT_PAID, CHECKOUT_FAILED
 * 
 * EXEMPLO DE CONFIGURAÇÃO COMPLETA:
 * 
 * $SUMUP_API_KEY = "sup_sk_1234567890abcdef";
 * $SUMUP_CLIENT_ID = "com.exemplo.pdv";
 * $SUMUP_CLIENT_SECRET = "abcdef1234567890";
 * $SUMUP_MERCHANT_CODE = "MCODE123";
 * $SUMUP_SANDBOX = false; // Produção
 * $SUMUP_WEBHOOK_SECRET = "webhook_secret_123";
 */

?>
