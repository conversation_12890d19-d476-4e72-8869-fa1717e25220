<?php
// Script para investigar o problema do saldo final incorreto
// URL: http://localhost:8010/sistema/debug_saldo_final.php

require_once 'config.php';

echo "<h1>🔍 Investigação do Problema do Saldo Final</h1>\n";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>📊 Análise dos Dados Atuais</h2>\n";
    
    // Buscar os últimos 10 registros fechados
    $stmt = $pdo->prepare("
        SELECT 
            r.id,
            r.date as abertura,
            r.closed_at as fechamento,
            CONCAT(u.first_name, ' ', u.last_name) as usuario,
            r.cash_in_hand as saldo_inicial,
            r.total_cash,
            r.total_stripe,
            r.total_CC,
            r.total_pix,
            r.total_reforco,
            r.total_sangrias,
            -- C<PERSON>l<PERSON>lo atual do sistema (INCORRETO)
            COALESCE(r.total_cash, 0) as saldo_final_atual,
            -- Cálculo correto do saldo final
            (r.cash_in_hand + COALESCE(r.total_cash, 0) - COALESCE(r.cash_in_hand, 0) + COALESCE(r.total_reforco, 0) - COALESCE(r.total_sangrias, 0)) as saldo_final_correto,
            -- Cálculo do dinheiro atual (INCORRETO)
            (COALESCE(r.total_cash, 0) - COALESCE(r.cash_in_hand, 0) + COALESCE(r.total_sangrias, 0) - COALESCE(r.total_reforco, 0)) as dinheiro_atual,
            -- Cálculo correto do dinheiro (apenas vendas em dinheiro)
            (COALESCE(r.total_cash, 0) - COALESCE(r.cash_in_hand, 0)) as dinheiro_correto
        FROM tec_registers r
        LEFT JOIN tec_users u ON r.user_id = u.id
        WHERE r.status = 'close'
        ORDER BY r.id DESC
        LIMIT 10
    ");
    $stmt->execute();
    $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($registers) {
        echo "<div style='overflow-x: auto;'>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th>ID</th><th>Usuário</th><th>Saldo Inicial</th><th>Total Cash</th><th>Reforço</th><th>Sangria</th>\n";
        echo "<th style='background: #ffebee;'>Saldo Final (Atual)</th><th style='background: #e8f5e8;'>Saldo Final (Correto)</th>\n";
        echo "<th style='background: #ffebee;'>Dinheiro (Atual)</th><th style='background: #e8f5e8;'>Dinheiro (Correto)</th>\n";
        echo "<th>Problema?</th>\n";
        echo "</tr>\n";
        
        foreach ($registers as $register) {
            $problema = '';
            $cor_linha = '';
            
            // Verificar se há problemas
            if ($register['saldo_final_atual'] != $register['saldo_final_correto']) {
                $problema .= 'Saldo Final Incorreto; ';
                $cor_linha = 'background: #ffebee;';
            }
            
            if ($register['dinheiro_atual'] != $register['dinheiro_correto']) {
                $problema .= 'Dinheiro Incorreto; ';
                $cor_linha = 'background: #ffebee;';
            }
            
            echo "<tr style='$cor_linha'>\n";
            echo "<td>" . $register['id'] . "</td>\n";
            echo "<td>" . $register['usuario'] . "</td>\n";
            echo "<td>R$ " . number_format($register['saldo_inicial'], 2, ',', '.') . "</td>\n";
            echo "<td>R$ " . number_format($register['total_cash'], 2, ',', '.') . "</td>\n";
            echo "<td>R$ " . number_format($register['total_reforco'], 2, ',', '.') . "</td>\n";
            echo "<td>R$ " . number_format($register['total_sangrias'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #ffebee;'>R$ " . number_format($register['saldo_final_atual'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #e8f5e8;'>R$ " . number_format($register['saldo_final_correto'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #ffebee;'>R$ " . number_format($register['dinheiro_atual'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #e8f5e8;'>R$ " . number_format($register['dinheiro_correto'], 2, ',', '.') . "</td>\n";
            echo "<td>" . ($problema ? "❌ $problema" : "✅ OK") . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        echo "</div>\n";
    }
    
    echo "<h2>🔍 Análise do Problema</h2>\n";
    echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<h3>❌ Problema Identificado no Controller Reports.php (linha 283):</h3>\n";
    echo "<pre style='background: #ffebee; padding: 10px; border: 1px solid #f44336;'>\n";
    echo "// INCORRETO - Saldo Final:\n";
    echo "COALESCE(total_cash, 0) as saldo_final\n\n";
    echo "// INCORRETO - Dinheiro:\n";
    echo "(COALESCE(total_cash, 0) - COALESCE(cash_in_hand, 0) + COALESCE(total_sangrias, 0) - COALESCE(total_reforco, 0)) as dinheiro\n";
    echo "</pre>\n";
    echo "</div>\n";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<h3>✅ Fórmula Correta:</h3>\n";
    echo "<pre style='background: #e8f5e8; padding: 10px; border: 1px solid #4caf50;'>\n";
    echo "// CORRETO - Saldo Final:\n";
    echo "Saldo Final = Saldo Inicial + Dinheiro (vendas) + Reforço - Sangria\n";
    echo "(cash_in_hand + (total_cash - cash_in_hand) + total_reforco - total_sangrias) as saldo_final\n\n";
    echo "// CORRETO - Dinheiro (apenas vendas em dinheiro):\n";
    echo "(total_cash - cash_in_hand) as dinheiro\n";
    echo "</pre>\n";
    echo "</div>\n";
    
    echo "<h2>🛠️ Solução</h2>\n";
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<p><strong>Para corrigir o problema, é necessário:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Alterar a consulta SQL no arquivo <code>app/controllers/Reports.php</code> linha 283</li>\n";
    echo "<li>Corrigir o cálculo do <strong>Saldo Final</strong></li>\n";
    echo "<li>Corrigir o cálculo do <strong>Dinheiro</strong></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
    if (isset($_POST['aplicar_correcao'])) {
        echo "<h2>🔧 Aplicando Correção...</h2>\n";
        echo "<p style='color: green; font-weight: bold;'>A correção será aplicada no arquivo Reports.php</p>\n";
        echo "<p><a href='#' onclick='window.location.reload();'>🔄 Recarregar página para ver resultado</a></p>\n";
    } else {
        echo "<form method='post' style='margin: 20px 0;'>\n";
        echo "<button type='submit' name='aplicar_correcao' value='1' style='background: #4caf50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>\n";
        echo "🔧 Aplicar Correção no Reports.php\n";
        echo "</button>\n";
        echo "</form>\n";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; color: #d32f2f;'>\n";
    echo "<h3>❌ Erro de Conexão:</h3>\n";
    echo "<p>" . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>
