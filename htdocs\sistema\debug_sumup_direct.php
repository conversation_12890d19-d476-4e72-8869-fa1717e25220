<?php
/**
 * Debug SumUp - Teste Direto com Captura de Erros
 * 
 * Este script testa a integração SumUp capturando todos os erros
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

// Capturar todos os erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Buffer de saída para capturar erros
ob_start();

// Incluir configurações
require_once 'config.php';

// Definir header JSON se for requisição AJAX
if (isset($_POST['test_action'])) {
    header('Content-Type: application/json');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug SumUp - <PERSON><PERSON> Diret<PERSON></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .log-output { background: #000; color: #0f0; padding: 10px; font-family: monospace; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 Debug SumUp - Teste Direto</h1>
    
    <?php if (!isset($_POST['test_action'])): ?>
    
    <div class="test-section">
        <h2>1. Configurações</h2>
        <?php
        echo "<ul>";
        echo "<li><strong>SUMUP_API_KEY:</strong> " . (empty($SUMUP_API_KEY) ? "❌ Vazio" : "✅ " . substr($SUMUP_API_KEY, 0, 15) . "...") . "</li>";
        echo "<li><strong>SUMUP_MERCHANT_CODE:</strong> " . (empty($SUMUP_MERCHANT_CODE) ? "❌ Vazio" : "✅ " . $SUMUP_MERCHANT_CODE) . "</li>";
        echo "<li><strong>SUMUP_PAY_TO_EMAIL:</strong> " . (empty($SUMUP_PAY_TO_EMAIL) ? "❌ Vazio" : "✅ " . $SUMUP_PAY_TO_EMAIL) . "</li>";
        echo "<li><strong>SUMUP_SANDBOX:</strong> " . ($SUMUP_SANDBOX ? "✅ true" : "⚠️ false") . "</li>";
        echo "<li><strong>SUMUP_API_URL:</strong> " . $SUMUP_API_URL . "</li>";
        echo "</ul>";
        ?>
    </div>

    <div class="test-section">
        <h2>2. Teste de Carregamento do Modelo</h2>
        <button onclick="testModelLoad()">Testar Carregamento do Modelo</button>
        <div id="model-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Teste de Autenticação</h2>
        <button onclick="testAuth()">Testar Autenticação</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Teste de Criação de Checkout</h2>
        <button onclick="testCheckout()">Testar Criação de Checkout</button>
        <div id="checkout-result"></div>
    </div>

    <div class="test-section">
        <h2>5. Log de Debug</h2>
        <div id="debug-log" class="log-output">Aguardando testes...</div>
    </div>

    <script>
    function logDebug(message) {
        const logDiv = document.getElementById('debug-log');
        const timestamp = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${timestamp}] ${message}\n`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }

    function testModelLoad() {
        logDebug('🔄 Testando carregamento do modelo...');
        const resultDiv = document.getElementById('model-result');
        
        fetch('debug_sumup_direct.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'test_action=load_model'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `<p class="success">✅ ${data.message}</p>`;
                logDebug('✅ Modelo carregado com sucesso');
            } else {
                resultDiv.innerHTML = `<p class="error">❌ ${data.message}</p>`;
                logDebug('❌ Erro ao carregar modelo: ' + data.message);
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `<p class="error">❌ Erro: ${error}</p>`;
            logDebug('❌ Erro de rede: ' + error);
        });
    }

    function testAuth() {
        logDebug('🔄 Testando autenticação...');
        const resultDiv = document.getElementById('auth-result');
        
        fetch('debug_sumup_direct.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'test_action=test_auth'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `<p class="success">✅ ${data.message}</p><pre>${JSON.stringify(data.data, null, 2)}</pre>`;
                logDebug('✅ Autenticação OK');
            } else {
                resultDiv.innerHTML = `<p class="error">❌ ${data.message}</p>`;
                logDebug('❌ Erro de autenticação: ' + data.message);
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `<p class="error">❌ Erro: ${error}</p>`;
            logDebug('❌ Erro de rede: ' + error);
        });
    }

    function testCheckout() {
        logDebug('🔄 Testando criação de checkout...');
        const resultDiv = document.getElementById('checkout-result');
        
        fetch('debug_sumup_direct.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'test_action=test_checkout&amount=10.50'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <p class="success">✅ ${data.message}</p>
                    <p><strong>Checkout ID:</strong> ${data.checkout_id}</p>
                    <p><strong>URL:</strong> <a href="${data.checkout_url}" target="_blank">${data.checkout_url}</a></p>
                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                `;
                logDebug('✅ Checkout criado: ' + data.checkout_id);
            } else {
                resultDiv.innerHTML = `<p class="error">❌ ${data.message}</p>`;
                if (data.debug) {
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data.debug, null, 2)}</pre>`;
                }
                logDebug('❌ Erro no checkout: ' + data.message);
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `<p class="error">❌ Erro: ${error}</p>`;
            logDebug('❌ Erro de rede: ' + error);
        });
    }
    </script>

    <?php else: ?>

    <?php
    // Processar testes AJAX
    try {
        $test_action = $_POST['test_action'];
        
        switch ($test_action) {
            case 'load_model':
                // Tentar carregar o modelo SumUp
                if (!file_exists('app/models/Sumup_model.php')) {
                    echo json_encode(['success' => false, 'message' => 'Arquivo Sumup_model.php não encontrado']);
                    exit;
                }
                
                // Simular carregamento do CodeIgniter
                define('BASEPATH', __DIR__ . '/lib/');
                require_once 'app/models/Sumup_model.php';
                
                echo json_encode(['success' => true, 'message' => 'Modelo carregado com sucesso']);
                break;
                
            case 'test_auth':
                // Testar autenticação direta
                $api_url = $SUMUP_API_URL;
                $access_token = null;
                
                if (!empty($SUMUP_API_KEY) && strpos($SUMUP_API_KEY, 'sup_sk_') === 0) {
                    $access_token = $SUMUP_API_KEY;
                } else {
                    echo json_encode(['success' => false, 'message' => 'Chave privada não encontrada']);
                    exit;
                }
                
                // Testar API /me
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url . '/v0.1/me');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $access_token,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo json_encode(['success' => false, 'message' => 'cURL Error: ' . $error]);
                } elseif ($http_code == 200) {
                    $data = json_decode($response, true);
                    echo json_encode(['success' => true, 'message' => 'Autenticação OK', 'data' => $data]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'HTTP ' . $http_code . ': ' . $response]);
                }
                break;
                
            case 'test_checkout':
                // Testar criação de checkout
                $amount = $_POST['amount'] ?? 10.50;
                
                $checkout_data = [
                    'checkout_reference' => 'debug_' . uniqid(),
                    'amount' => floatval($amount),
                    'currency' => 'BRL',
                    'description' => 'Teste Debug'
                ];
                
                // Adicionar merchant_code OU pay_to_email
                if (!empty($SUMUP_MERCHANT_CODE)) {
                    $checkout_data['merchant_code'] = $SUMUP_MERCHANT_CODE;
                } elseif (!empty($SUMUP_PAY_TO_EMAIL)) {
                    $checkout_data['pay_to_email'] = $SUMUP_PAY_TO_EMAIL;
                } else {
                    echo json_encode(['success' => false, 'message' => 'Nem merchant_code nem pay_to_email configurados']);
                    exit;
                }
                
                $access_token = $SUMUP_API_KEY;
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $SUMUP_API_URL . '/v0.1/checkouts');
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($checkout_data));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $access_token,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo json_encode(['success' => false, 'message' => 'cURL Error: ' . $error]);
                } elseif ($http_code >= 200 && $http_code < 300) {
                    $data = json_decode($response, true);
                    echo json_encode([
                        'success' => true,
                        'message' => 'Checkout criado com sucesso',
                        'checkout_id' => $data['id'],
                        'checkout_url' => $SUMUP_API_URL . '/v0.1/checkouts/' . $data['id'],
                        'data' => $data
                    ]);
                } else {
                    $data = json_decode($response, true);
                    echo json_encode([
                        'success' => false,
                        'message' => 'HTTP ' . $http_code . ': ' . ($data['message'] ?? 'Erro desconhecido'),
                        'debug' => [
                            'http_code' => $http_code,
                            'request_data' => $checkout_data,
                            'response' => $data
                        ]
                    ]);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'Ação não reconhecida']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Exceção: ' . $e->getMessage()]);
    }
    ?>

    <?php endif; ?>

</body>
</html>
