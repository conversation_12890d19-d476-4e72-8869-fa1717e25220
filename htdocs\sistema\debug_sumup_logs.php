<?php
/**
 * Debug SumUp - Visualizar Logs
 * 
 * Este script mostra os logs relacionados ao SumUp para debug
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug SumUp - Logs</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log-entry { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }
        .debug { border-left-color: #007cba; background: #f0f8ff; }
        .error { border-left-color: #dc3545; background: #fff5f5; }
        .info { border-left-color: #28a745; background: #f0fff4; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        .timestamp { color: #666; font-size: 0.9em; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Debug SumUp - Logs</h1>
    
    <button onclick="location.reload()">🔄 Atualizar</button>
    <button onclick="clearLogs()">🗑️ Limpar Logs</button>
    
    <hr>
    
    <?php
    // Procurar arquivos de log
    $log_dirs = [
        'app/logs/',
        'application/logs/',
        '../logs/',
        '../../logs/'
    ];
    
    $log_files = [];
    foreach ($log_dirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . 'log-*.php');
            $log_files = array_merge($log_files, $files);
        }
    }
    
    if (empty($log_files)) {
        echo "<p>❌ Nenhum arquivo de log encontrado</p>";
        echo "<p>Procurei em: " . implode(', ', $log_dirs) . "</p>";
    } else {
        echo "<h2>📁 Arquivos de Log Encontrados:</h2>";
        foreach ($log_files as $file) {
            echo "<p>📄 " . $file . " (" . date('d/m/Y H:i:s', filemtime($file)) . ")</p>";
        }
        
        // Pegar o arquivo mais recente
        $latest_log = end($log_files);
        
        echo "<h2>📋 Últimas Entradas de Log (SumUp):</h2>";
        
        if (file_exists($latest_log)) {
            $log_content = file_get_contents($latest_log);
            $lines = explode("\n", $log_content);
            
            // Filtrar apenas linhas relacionadas ao SumUp
            $sumup_lines = [];
            foreach ($lines as $line) {
                if (stripos($line, 'sumup') !== false) {
                    $sumup_lines[] = $line;
                }
            }
            
            if (empty($sumup_lines)) {
                echo "<p>ℹ️ Nenhuma entrada de log relacionada ao SumUp encontrada</p>";
                echo "<p><strong>Dica:</strong> Faça uma tentativa de pagamento SumUp no PDV para gerar logs</p>";
            } else {
                // Mostrar apenas as últimas 20 entradas
                $recent_lines = array_slice($sumup_lines, -20);
                
                foreach ($recent_lines as $line) {
                    if (empty(trim($line))) continue;
                    
                    // Extrair informações da linha de log
                    $class = 'log-entry';
                    if (stripos($line, 'ERROR') !== false) {
                        $class .= ' error';
                    } elseif (stripos($line, 'DEBUG') !== false) {
                        $class .= ' debug';
                    } else {
                        $class .= ' info';
                    }
                    
                    echo "<div class='{$class}'>";
                    echo "<pre>" . htmlspecialchars($line) . "</pre>";
                    echo "</div>";
                }
            }
        } else {
            echo "<p>❌ Não foi possível ler o arquivo de log</p>";
        }
    }
    ?>
    
    <hr>
    
    <h2>🧪 Teste Rápido de Configuração</h2>
    
    <?php
    // Incluir configurações
    require_once 'config.php';
    
    echo "<h3>Configurações Atuais:</h3>";
    echo "<ul>";
    echo "<li><strong>SUMUP_API_KEY:</strong> " . (empty($SUMUP_API_KEY) ? "❌ Vazio" : "✅ Configurado (" . substr($SUMUP_API_KEY, 0, 10) . "...)") . "</li>";
    echo "<li><strong>SUMUP_MERCHANT_CODE:</strong> " . (empty($SUMUP_MERCHANT_CODE) ? "❌ Vazio" : "✅ " . $SUMUP_MERCHANT_CODE) . "</li>";
    echo "<li><strong>SUMUP_PAY_TO_EMAIL:</strong> " . (empty($SUMUP_PAY_TO_EMAIL) ? "❌ Vazio" : "✅ " . $SUMUP_PAY_TO_EMAIL) . "</li>";
    echo "<li><strong>SUMUP_SANDBOX:</strong> " . ($SUMUP_SANDBOX ? "✅ true (teste)" : "⚠️ false (produção)") . "</li>";
    echo "</ul>";
    
    // Verificar se o modelo pode ser carregado
    if (file_exists('app/models/Sumup_model.php')) {
        echo "<p>✅ Arquivo Sumup_model.php encontrado</p>";
    } else {
        echo "<p>❌ Arquivo Sumup_model.php NÃO encontrado</p>";
    }
    ?>
    
    <h2>📞 Próximos Passos</h2>
    <ol>
        <li><strong>Faça uma tentativa de pagamento</strong> no PDV usando SumUp</li>
        <li><strong>Atualize esta página</strong> para ver os logs gerados</li>
        <li><strong>Analise os logs</strong> para identificar o erro específico</li>
        <li><strong>Se não aparecer logs</strong>, verifique se o logging está habilitado no CodeIgniter</li>
    </ol>
    
    <script>
    function clearLogs() {
        if (confirm('Tem certeza que deseja limpar os logs?')) {
            fetch('debug_sumup_logs.php?action=clear', {method: 'POST'})
            .then(() => location.reload());
        }
    }
    </script>
    
    <hr>
    <p><small>
        <strong>Data:</strong> <?php echo date('d/m/Y H:i:s'); ?><br>
        <strong>Nota:</strong> Este script deve ser removido em produção.
    </small></p>
</body>
</html>

<?php
// Ação para limpar logs
if (isset($_GET['action']) && $_GET['action'] === 'clear' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    foreach ($log_files as $file) {
        if (file_exists($file)) {
            file_put_contents($file, "<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>\n\n");
        }
    }
    echo "Logs limpos!";
    exit;
}
?>
