<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SumUp Bridge - Integração com Máquina</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .amount {
            font-size: 2.5em;
            color: #2c5aa0;
            margin: 20px 0;
            font-weight: bold;
        }
        .description {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #2c5aa0;
            color: white;
        }
        .btn-primary:hover {
            background: #1e3f73;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background: #e7f3ff;
            border-radius: 5px;
            border-left: 4px solid #2c5aa0;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .qr-code {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏪 Pagamento SumUp Solo</h1>
        
        <div class="amount" id="amount">R$ 0,00</div>
        <div class="description" id="description">Carregando...</div>
        
        <div class="qr-code" id="qrcode" style="display: none;">
            <!-- QR Code será inserido aqui -->
        </div>
        
        <div>
            <a href="#" id="openApp" class="btn btn-primary">
                📱 Abrir App SumUp
            </a>
            <a href="#" id="webFallback" class="btn btn-secondary">
                🌐 Usar Navegador
            </a>
        </div>
        
        <div class="instructions">
            <h3>📋 Instruções:</h3>
            <ol>
                <li><strong>Clique "Abrir App SumUp"</strong> no dispositivo com app instalado</li>
                <li><strong>Conecte sua SumUp Solo</strong> via Bluetooth/USB</li>
                <li><strong>Confirme o pagamento</strong> na máquina</li>
                <li><strong>Aguarde</strong> a confirmação automática</li>
            </ol>
        </div>
        
        <div class="status" id="status"></div>
        
        <div style="margin-top: 30px;">
            <button onclick="window.close()" class="btn btn-secondary">❌ Cancelar</button>
            <button onclick="checkStatus()" class="btn btn-primary">🔄 Verificar Status</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Obter parâmetros da URL
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get('amount') || '0.00';
        const description = urlParams.get('description') || 'Pagamento PDV';
        const deepLink = urlParams.get('deep_link') || '';
        const webFallback = urlParams.get('web_fallback') || '';
        const checkoutId = urlParams.get('checkout_id') || '';
        
        // Atualizar interface
        document.getElementById('amount').textContent = 'R$ ' + parseFloat(amount).toLocaleString('pt-BR', {minimumFractionDigits: 2});
        document.getElementById('description').textContent = description;
        document.getElementById('openApp').href = deepLink;
        document.getElementById('webFallback').href = webFallback;
        
        // Gerar QR Code
        if (deepLink) {
            QRCode.toCanvas(document.createElement('canvas'), deepLink, function (error, canvas) {
                if (!error) {
                    document.getElementById('qrcode').appendChild(canvas);
                    document.getElementById('qrcode').style.display = 'block';
                }
            });
        }
        
        // Função para verificar status do pagamento
        function checkStatus() {
            if (!checkoutId) {
                showStatus('error', 'ID do checkout não encontrado');
                return;
            }
            
            fetch(`/sistema/index.php/pos/check_sumup_status?checkout_id=${checkoutId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.status === 'PAID') {
                            showStatus('success', '✅ Pagamento confirmado! Fechando em 3 segundos...');
                            setTimeout(() => {
                                window.close();
                                if (window.opener) {
                                    window.opener.location.reload();
                                }
                            }, 3000);
                        } else if (data.status === 'FAILED') {
                            showStatus('error', '❌ Pagamento falhou. Tente novamente.');
                        } else {
                            showStatus('info', '⏳ Pagamento pendente. Aguardando confirmação...');
                        }
                    } else {
                        showStatus('error', 'Erro ao verificar status: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    showStatus('error', 'Erro de conexão: ' + error.message);
                });
        }
        
        function showStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + type;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }
        
        // Verificar status automaticamente a cada 5 segundos
        if (checkoutId) {
            setInterval(checkStatus, 5000);
        }
        
        // Detectar se voltou do app
        window.addEventListener('focus', function() {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
