<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento SumUp - Integração com Máquina Física</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.2em;
        }
        
        .amount-display {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #28a745;
        }
        
        .amount {
            font-size: 3em;
            font-weight: bold;
            color: #28a745;
        }
        
        .merchant-info {
            color: #666;
            margin-top: 10px;
        }
        
        .important-notice {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .important-notice h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .important-notice p {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .payment-options {
            display: grid;
            gap: 20px;
            margin: 30px 0;
        }
        
        .option-card {
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 25px;
            text-align: left;
        }
        
        .option-card.primary {
            border-color: #007bff;
            background: #f8f9ff;
        }
        
        .option-card.secondary {
            border-color: #6c757d;
            background: #f8f9fa;
        }
        
        .option-card h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #6c757d;
            color: #6c757d;
        }
        
        .qr-container {
            margin: 20px 0;
            text-align: center;
        }
        
        .qr-text {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .instructions {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h4, .instructions h5 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .instructions ol, .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .tech-note {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .tech-note h5 {
            color: #0c5460;
        }
        
        .tech-note p, .tech-note li {
            color: #0c5460;
            font-size: 14px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
        }
        
        .debug-info h4 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        #debugContent {
            font-family: monospace;
            font-size: 12px;
            background: #fff;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .amount {
                font-size: 2.5em;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Pagamento SumUp</h1>
            <div class="amount-display">
                <div class="amount" id="amount">R$ 0,00</div>
                <div class="merchant-info">
                    <small>Merchant: <span id="merchantCode">MVTC6RDA</span></small>
                </div>
            </div>
        </div>

        <div class="status info" id="status">
            ⏳ Preparando pagamento...
        </div>

        <div class="important-notice">
            <h3>⚠️ IMPORTANTE</h3>
            <p><strong>A API REST da SumUp não controla máquinas físicas diretamente.</strong></p>
            <p>Para usar sua <strong>SumUp Solo</strong>, você precisa de um <strong>dispositivo móvel</strong> (celular/tablet) com o app SumUp instalado.</p>
        </div>

        <div class="payment-options">
            <div class="option-card primary">
                <h3>📱 Opção 1: Máquina Física via Mobile</h3>
                <p>Use sua SumUp Solo conectada ao celular/tablet</p>
                <div class="button-group">
                    <button id="openAppBtn" class="btn btn-primary">
                        📱 Abrir App SumUp
                    </button>
                    <button id="showLinkBtn" class="btn btn-secondary">
                        🔗 Ver Link
                    </button>
                </div>
                <div class="qr-container">
                    <div id="qrcode"></div>
                    <p class="qr-text">Escaneie com o celular</p>
                </div>
            </div>

            <div class="option-card secondary">
                <h3>🌐 Opção 2: Checkout Web</h3>
                <p>Pagamento online sem máquina física</p>
                <button id="webCheckoutBtn" class="btn btn-outline">
                    🌐 Abrir Checkout Web
                </button>
            </div>
        </div>

        <div class="instructions">
            <h4>📋 Como usar a máquina física:</h4>
            <ol>
                <li>No <strong>celular/tablet</strong>: Instale o <strong>app SumUp</strong></li>
                <li>No app: Conecte sua <strong>SumUp Solo</strong> via Bluetooth</li>
                <li>Clique em <strong>"📱 Abrir App SumUp"</strong> ou escaneie o QR Code</li>
                <li>O <strong>valor aparecerá na máquina</strong> para pagamento</li>
                <li>Cliente insere o cartão e digita a senha</li>
            </ol>
            
            <div class="tech-note">
                <h5>🔧 Nota Técnica:</h5>
                <p>A SumUp não oferece API REST pública para controle direto de card readers. A integração com máquinas físicas requer:</p>
                <ul>
                    <li><strong>iOS/Android SDK</strong> para apps nativos</li>
                    <li><strong>Deep links</strong> para dispositivos móveis (como implementado aqui)</li>
                    <li><strong>Checkout Web</strong> para pagamentos online</li>
                </ul>
            </div>
        </div>

        <div class="debug-info" id="debugInfo" style="display: none;">
            <h4>🔧 Informações de Debug:</h4>
            <div id="debugContent"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Obter parâmetros da URL
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get('amount') || '0.00';
        const description = urlParams.get('description') || 'Pagamento PDV';
        const deepLink = urlParams.get('deep_link') || '';
        const webFallback = urlParams.get('web_fallback') || '';
        const checkoutId = urlParams.get('checkout_id') || '';
        const merchantCode = urlParams.get('merchant_code') || 'MVTC6RDA';
        
        // Atualizar interface
        document.getElementById('amount').textContent = 'R$ ' + parseFloat(amount).toLocaleString('pt-BR', {minimumFractionDigits: 2});
        document.getElementById('merchantCode').textContent = merchantCode;
        
        // Mostrar informações de debug
        const debugContent = {
            amount: amount,
            description: description,
            deepLink: deepLink,
            webFallback: webFallback,
            checkoutId: checkoutId,
            merchantCode: merchantCode,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };
        
        document.getElementById('debugContent').textContent = JSON.stringify(debugContent, null, 2);
        
        // Gerar QR Code
        if (deepLink) {
            QRCode.toCanvas(document.createElement('canvas'), deepLink, function (error, canvas) {
                if (!error) {
                    canvas.style.maxWidth = '200px';
                    canvas.style.height = 'auto';
                    document.getElementById('qrcode').appendChild(canvas);
                }
            });
        }
        
        // Event listeners
        document.getElementById('openAppBtn').addEventListener('click', function() {
            if (deepLink) {
                showStatus('info', '🚀 Tentando abrir o app SumUp...');
                
                // Tentar abrir o deep link
                window.location.href = deepLink;
                
                // Mostrar instruções após um delay
                setTimeout(function() {
                    showStatus('info', '⚠️ Se o app não abriu:\n1. Certifique-se que o app SumUp está instalado\n2. Tente escanear o QR Code\n3. Use o checkout web como alternativa');
                }, 3000);
            } else {
                showStatus('error', '❌ Deep link não disponível');
            }
        });
        
        document.getElementById('showLinkBtn').addEventListener('click', function() {
            if (deepLink) {
                showStatus('info', '🔗 Deep Link:\n' + deepLink);
                document.getElementById('debugInfo').style.display = 'block';
            } else {
                showStatus('error', '❌ Deep link não disponível');
            }
        });
        
        document.getElementById('webCheckoutBtn').addEventListener('click', function() {
            if (webFallback) {
                showStatus('info', '🌐 Redirecionando para checkout web...');
                window.open(webFallback, '_blank');
            } else {
                showStatus('error', '❌ Checkout web não disponível');
            }
        });
        
        function showStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = 'status ' + type;
            statusEl.textContent = message;
        }
        
        // Inicializar
        showStatus('info', '✅ Pagamento preparado! Escolha uma opção acima.');
        
        // Log para debug
        console.log('SumUp Bridge - Parâmetros:', debugContent);
    </script>
</body>
</html>
