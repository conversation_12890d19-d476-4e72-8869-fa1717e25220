<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guia de Integração SumUp - Máquina Física</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        
        .header h1 {
            color: #007bff;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #007bff;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .section h3 {
            color: #495057;
            font-size: 1.4em;
            margin-bottom: 15px;
            margin-top: 25px;
        }
        
        .alert {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .step-list {
            counter-reset: step-counter;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding-left: 40px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #007bff;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .feature-card.available {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .feature-card.limited {
            border-color: #ffc107;
            background: #fffdf5;
        }
        
        .feature-card.unavailable {
            border-color: #dc3545;
            background: #fff5f5;
        }
        
        .feature-card h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .feature-card.available h4 {
            color: #28a745;
        }
        
        .feature-card.limited h4 {
            color: #856404;
        }
        
        .feature-card.unavailable h4 {
            color: #dc3545;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Guia de Integração SumUp</h1>
            <p>Como integrar sua máquina SumUp Solo com o sistema PDV</p>
        </div>

        <div class="alert alert-warning">
            <h3>⚠️ DESCOBERTA IMPORTANTE</h3>
            <p><strong>A API REST da SumUp não oferece controle direto de máquinas físicas (card readers).</strong></p>
            <p>Após análise da documentação oficial, confirmamos que os endpoints para controle direto de card readers não estão disponíveis na API pública.</p>
        </div>

        <div class="section">
            <h2>🔍 Análise da Situação Atual</h2>
            
            <h3>Merchant Code Configurado:</h3>
            <div class="code-block">MVTC6RDA</div>
            
            <h3>O que foi implementado:</h3>
            <ul>
                <li>✅ Integração completa com SumUp Checkout (pagamentos web)</li>
                <li>✅ Sistema de webhooks para confirmação automática</li>
                <li>✅ Deep links para dispositivos móveis</li>
                <li>✅ Interface bridge para múltiplas opções de pagamento</li>
                <li>❌ Controle direto de máquina física via API REST (não disponível)</li>
            </ul>
        </div>

        <div class="section">
            <h2>🎯 Opções Disponíveis para Máquina Física</h2>
            
            <div class="feature-grid">
                <div class="feature-card limited">
                    <h4>📱 Deep Links (Implementado)</h4>
                    <p><strong>Funciona em:</strong> iOS/Android</p>
                    <p><strong>Requer:</strong> App SumUp instalado</p>
                    <p><strong>Limitação:</strong> Não funciona no Windows</p>
                </div>
                
                <div class="feature-card available">
                    <h4>📱 SDK Nativo</h4>
                    <p><strong>Funciona em:</strong> iOS/Android</p>
                    <p><strong>Requer:</strong> App nativo</p>
                    <p><strong>Controle:</strong> Total da máquina</p>
                </div>
                
                <div class="feature-card unavailable">
                    <h4>🖥️ API REST Direta</h4>
                    <p><strong>Status:</strong> Não disponível</p>
                    <p><strong>Motivo:</strong> SumUp não oferece</p>
                    <p><strong>Alternativa:</strong> Use SDK ou deep links</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 Como Usar a Máquina SumUp Solo</h2>
            
            <div class="alert alert-info">
                <h3>💡 Solução Recomendada</h3>
                <p>Para usar sua SumUp Solo com o sistema PDV, siga estes passos:</p>
            </div>
            
            <ol class="step-list">
                <li><strong>Configure um dispositivo móvel:</strong>
                    <ul>
                        <li>Instale o app SumUp no celular/tablet</li>
                        <li>Faça login com sua conta SumUp</li>
                        <li>Conecte a SumUp Solo via Bluetooth</li>
                    </ul>
                </li>
                
                <li><strong>No sistema PDV:</strong>
                    <ul>
                        <li>Adicione produtos à venda</li>
                        <li>Clique em "Finalizar Venda"</li>
                        <li>Escolha "SumUp" como forma de pagamento</li>
                        <li>Selecione "OK" para máquina física</li>
                    </ul>
                </li>
                
                <li><strong>Na página que abrir:</strong>
                    <ul>
                        <li>Clique em "📱 Abrir App SumUp" no dispositivo móvel</li>
                        <li>Ou escaneie o QR Code com o celular/tablet</li>
                        <li>O valor aparecerá na máquina SumUp Solo</li>
                    </ul>
                </li>
                
                <li><strong>Processamento do pagamento:</strong>
                    <ul>
                        <li>Cliente insere o cartão na máquina</li>
                        <li>Cliente digita a senha</li>
                        <li>Pagamento é processado automaticamente</li>
                        <li>Sistema PDV recebe confirmação via webhook</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="section">
            <h2>🔧 Detalhes Técnicos</h2>
            
            <h3>Fluxo de Integração Atual:</h3>
            <div class="code-block">
PDV → Deep Link → App SumUp → SumUp Solo → Pagamento → Webhook → PDV
            </div>
            
            <h3>Limitações da API REST:</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Funcionalidade</th>
                        <th>API REST</th>
                        <th>SDK Nativo</th>
                        <th>Deep Links</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Controle direto da máquina</td>
                        <td>❌ Não</td>
                        <td>✅ Sim</td>
                        <td>✅ Sim (via app)</td>
                    </tr>
                    <tr>
                        <td>Funciona no Windows</td>
                        <td>❌ N/A</td>
                        <td>❌ Não</td>
                        <td>❌ Não</td>
                    </tr>
                    <tr>
                        <td>Funciona no Mobile</td>
                        <td>❌ N/A</td>
                        <td>✅ Sim</td>
                        <td>✅ Sim</td>
                    </tr>
                    <tr>
                        <td>Checkout Web</td>
                        <td>✅ Sim</td>
                        <td>✅ Sim</td>
                        <td>✅ Sim</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🚀 Próximos Passos</h2>
            
            <div class="alert alert-success">
                <h3>✅ Sistema Pronto para Uso</h3>
                <p>Sua integração SumUp está completa e funcional. Para usar a máquina física, siga o processo descrito acima.</p>
            </div>
            
            <h3>Opções de Melhoria:</h3>
            <ul>
                <li><strong>App Nativo:</strong> Desenvolver app móvel usando SDK SumUp para controle total</li>
                <li><strong>Terminal Dedicado:</strong> Usar tablet Android dedicado para pagamentos</li>
                <li><strong>Integração Híbrida:</strong> Combinar checkout web + deep links conforme necessário</li>
            </ul>
        </div>

        <div class="section">
            <h2>📞 Suporte e Recursos</h2>
            
            <div class="feature-grid">
                <div class="feature-card available">
                    <h4>📚 Documentação</h4>
                    <p>API oficial da SumUp</p>
                    <a href="https://developer.sumup.com/api" target="_blank" class="btn">Ver Documentação</a>
                </div>
                
                <div class="feature-card available">
                    <h4>🔑 Chaves de API</h4>
                    <p>Gerenciar credenciais</p>
                    <a href="https://me.sumup.com/developers" target="_blank" class="btn">Acessar Dashboard</a>
                </div>
                
                <div class="feature-card available">
                    <h4>📱 App SumUp</h4>
                    <p>Download para dispositivos</p>
                    <a href="https://sumup.com/app" target="_blank" class="btn btn-secondary">Download</a>
                </div>
            </div>
        </div>

        <div class="alert alert-info">
            <h3>💡 Resumo</h3>
            <p><strong>Sua integração está funcionando!</strong> Para usar a máquina SumUp Solo, você precisa de um dispositivo móvel com o app SumUp. O sistema PDV gera deep links que abrem o app e enviam o valor para a máquina física.</p>
            <p><strong>Merchant Code:</strong> MVTC6RDA</p>
            <p><strong>Status:</strong> ✅ Pronto para uso</p>
        </div>
    </div>
</body>
</html>
