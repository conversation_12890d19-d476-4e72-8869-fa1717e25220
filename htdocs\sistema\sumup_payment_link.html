<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link de Pagamento SumUp</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.2em;
        }
        
        .amount-display {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 3px solid #28a745;
        }

        .description-section {
            margin: 20px 0;
            text-align: left;
        }

        .description-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .description-section input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .description-section input:focus {
            outline: none;
            border-color: #007bff;
        }

        .update-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 8px;
        }

        .update-btn:hover {
            background: #138496;
        }
        
        .amount {
            font-size: 2.5em;
            font-weight: bold;
            color: #28a745;
        }
        
        .link-container {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            word-break: break-all;
        }
        
        .link-url {
            font-family: monospace;
            font-size: 14px;
            color: #495057;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin: 10px 0;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h3 {
            color: #0c5460;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            color: #0c5460;
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .qr-container {
            margin: 20px 0;
        }
        
        .qr-text {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .amount {
                font-size: 2em;
            }
            
            .btn {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 Link de Pagamento</h1>
            <div class="amount-display">
                <div class="amount" id="amount">R$ 0,00</div>
            </div>
        </div>

        <div class="description-section">
            <label for="descriptionInput">📝 Descrição do Pagamento:</label>
            <input type="text" id="descriptionInput" placeholder="Ex: Compra na loja, Serviço prestado..." maxlength="100">
            <button class="update-btn" onclick="updateDescription()">🔄 Atualizar Link</button>
        </div>

        <div class="status info">
            ✅ Link de pagamento gerado com sucesso!
        </div>

        <div class="link-container">
            <h3>🔗 Compartilhe este link com o cliente:</h3>
            <div class="link-url" id="paymentLink">
                Carregando link...
            </div>
            
            <div>
                <button id="copyBtn" class="btn btn-success" onclick="copyToClipboard()">
                    📋 Copiar Link
                </button>
            </div>
        </div>
        <div class="instructions">
            <h3>📱 Como o cliente deve pagar:</h3>
            <ol>
                <li><strong>Receber o link</strong> via WhatsApp, SMS ou email</li>
                <li><strong>Abrir o link</strong> no celular ou computador</li>
                <li><strong>Inserir dados do cartão</strong> na página segura da SumUp</li>
                <li><strong>Confirmar o pagamento</strong></li>
                <li><strong>Receber confirmação</strong> automática no sistema</li>
            </ol>
        </div>

        <div class="status info" id="statusMonitor">
            ⏳ Aguardando pagamento do cliente...
        </div>

        <div>
            <button onclick="window.close()" class="btn btn-secondary">❌ Fechar</button>
            <button id="refreshBtn" class="btn btn-primary">🔄 Verificar Status</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Obter parâmetros da URL
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get('amount') || '0.00';
        let paymentUrl = urlParams.get('url') || '';
        const checkoutId = urlParams.get('checkout_id') || '';

        // Atualizar interface
        document.getElementById('amount').textContent = 'R$ ' + parseFloat(amount).toLocaleString('pt-BR', {minimumFractionDigits: 2});
        document.getElementById('paymentLink').textContent = paymentUrl;

        // Definir descrição inicial
        const initialDescription = urlParams.get('description') || 'Pagamento PDV';
        document.getElementById('descriptionInput').value = initialDescription;
        
        // Gerar QR Code
        if (paymentUrl) {
            QRCode.toCanvas(document.createElement('canvas'), paymentUrl, function (error, canvas) {
                if (!error) {
                    canvas.style.maxWidth = '200px';
                    canvas.style.height = 'auto';
                    document.getElementById('qrcode').appendChild(canvas);
                }
            });
        }
        
        // Função para copiar link
        function copyToClipboard() {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(paymentUrl).then(function() {
                    // Feedback visual
                    const btn = document.getElementById('copyBtn');
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '✅ Copiado!';
                    btn.style.background = '#28a745';

                    setTimeout(function() {
                        btn.innerHTML = originalText;
                        btn.style.background = '#28a745';
                    }, 2000);

                    alert('✅ Link copiado para área de transferência!\n\nCompartilhe com o cliente via WhatsApp, SMS ou email.');
                }).catch(function() {
                    // Fallback
                    prompt('📋 Copie este link:', paymentUrl);
                });
            } else {
                // Fallback para navegadores antigos
                const textArea = document.createElement('textarea');
                textArea.value = paymentUrl;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('✅ Link copiado para área de transferência!');
                } catch (err) {
                    prompt('📋 Copie este link:', paymentUrl);
                }
                document.body.removeChild(textArea);
            }
        }

        // Função para atualizar descrição
        function updateDescription() {
            const newDescription = document.getElementById('descriptionInput').value.trim();
            if (!newDescription) {
                alert('⚠️ Por favor, digite uma descrição válida.');
                return;
            }

            // Aqui você pode fazer uma chamada AJAX para criar um novo checkout com a nova descrição
            // Por enquanto, apenas mostrar uma mensagem
            alert('ℹ️ Para alterar a descrição, é necessário gerar um novo link de pagamento.\n\nFeche esta janela e crie um novo pagamento com a descrição desejada.');
        }
        
        document.getElementById('refreshBtn').addEventListener('click', function() {
            checkPaymentStatus();
        });
        
        // Função para verificar status do pagamento
        function checkPaymentStatus() {
            if (!checkoutId) return;
            
            // Aqui você pode fazer uma chamada AJAX para verificar o status
            // Por enquanto, apenas uma simulação
            document.getElementById('statusMonitor').innerHTML = '🔄 Verificando status...';
            
            setTimeout(function() {
                document.getElementById('statusMonitor').innerHTML = '⏳ Aguardando pagamento do cliente...';
            }, 2000);
        }
        
        // Monitoramento automático (opcional)
        if (checkoutId) {
            setInterval(checkPaymentStatus, 30000); // Verificar a cada 30 segundos
        }
        
        // Log para debug
        console.log('Payment Link Page - Parâmetros:', {
            amount: amount,
            paymentUrl: paymentUrl,
            checkoutId: checkoutId
        });
    </script>
</body>
</html>
