<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SumUp - Opções de Pagamento</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .payment-option {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .payment-option:hover {
            border-color: #007bff;
            background: #f0f8ff;
            transform: translateY(-2px);
        }
        
        .payment-option h3 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✅ ";
            margin-right: 8px;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .highlight {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .merchant-info {
            background: #f0f8ff;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        
        .back-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-button:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💳 SumUp - Opções de Pagamento</h1>
        
        <div class="merchant-info">
            <h3>🏪 Sua Conta SumUp</h3>
            <p><strong>Merchant Code:</strong> <span class="highlight">MVTC6RDA</span></p>
            <p><strong>Nome:</strong> Ópera Perfumaria</p>
            <p><strong>Status:</strong> ✅ Ativa</p>
        </div>
        
        <div class="info-box">
            <h3>ℹ️ Como Funciona</h3>
            <p>A SumUp oferece <strong>automaticamente</strong> todas as opções de pagamento disponíveis para sua conta. Não é necessário configurar métodos específicos - o sistema detecta e apresenta as opções conforme sua configuração de merchant.</p>
        </div>
        
        <div class="payment-option">
            <h3>💳 Cartão de Crédito</h3>
            <ul class="feature-list">
                <li><strong>Parcelamento Automático:</strong> Até 12x conforme configuração da conta</li>
                <li><strong>Bandeiras:</strong> Visa, Mastercard, Elo, American Express</li>
                <li><strong>3D Secure:</strong> Autenticação segura automática</li>
                <li><strong>Taxa:</strong> Conforme contrato SumUp</li>
                <li><strong>Aprovação:</strong> Instantânea</li>
            </ul>
        </div>
        
        <div class="payment-option">
            <h3>💳 Cartão de Débito</h3>
            <ul class="feature-list">
                <li><strong>Débito à Vista:</strong> Pagamento imediato</li>
                <li><strong>Bandeiras:</strong> Visa Electron, Maestro, Elo Débito</li>
                <li><strong>Autenticação:</strong> PIN ou biometria</li>
                <li><strong>Taxa:</strong> Menor que crédito</li>
                <li><strong>Liquidação:</strong> D+1 ou D+2</li>
            </ul>
        </div>
        
        <div class="payment-option">
            <h3>🏦 PIX</h3>
            <ul class="feature-list">
                <li><strong>Disponibilidade:</strong> Se habilitado na conta SumUp</li>
                <li><strong>Velocidade:</strong> Pagamento instantâneo</li>
                <li><strong>Horário:</strong> 24/7, incluindo fins de semana</li>
                <li><strong>Taxa:</strong> Geralmente menor que cartões</li>
                <li><strong>Limite:</strong> Conforme banco do cliente</li>
            </ul>
        </div>
        
        <div class="payment-option">
            <h3>🔒 Segurança</h3>
            <ul class="feature-list">
                <li><strong>PCI DSS:</strong> Certificação de segurança</li>
                <li><strong>Tokenização:</strong> Dados do cartão não passam pelo seu sistema</li>
                <li><strong>SSL/TLS:</strong> Criptografia em todas as transações</li>
                <li><strong>Antifraude:</strong> Sistema automático da SumUp</li>
                <li><strong>Compliance:</strong> LGPD e regulamentações bancárias</li>
            </ul>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ Importante</h3>
            <p><strong>Parcelamento:</strong> O número de parcelas disponíveis depende da configuração da sua conta SumUp. Para habilitar ou alterar o parcelamento, acesse o dashboard da SumUp ou entre em contato com o suporte.</p>
            <p><strong>PIX:</strong> Para habilitar PIX, é necessário configurar na sua conta SumUp através do dashboard oficial.</p>
        </div>
        
        <div class="info-box">
            <h3>🎯 Configurações Recomendadas</h3>
            <p><strong>1. Parcelamento:</strong> Habilite até 12x para aumentar conversão</p>
            <p><strong>2. PIX:</strong> Ative para pagamentos instantâneos</p>
            <p><strong>3. 3D Secure:</strong> Mantenha ativo para segurança</p>
            <p><strong>4. Webhooks:</strong> Configure para confirmação automática</p>
        </div>
        
        <div class="merchant-info">
            <h3>📞 Suporte SumUp</h3>
            <p><strong>Dashboard:</strong> <a href="https://me.sumup.com" target="_blank">me.sumup.com</a></p>
            <p><strong>Documentação:</strong> <a href="https://developer.sumup.com" target="_blank">developer.sumup.com</a></p>
            <p><strong>Suporte:</strong> Através do dashboard ou app SumUp</p>
        </div>
        
        <a href="test_sumup.html" class="back-button">⬅️ Voltar aos Testes</a>
        <a href="javascript:window.close()" class="back-button">❌ Fechar</a>
    </div>
</body>
</html>
