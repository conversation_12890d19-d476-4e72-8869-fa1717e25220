<?php
/**
 * Teste de URLs de Checkout SumUp
 * 
 * Este script testa diferentes URLs para encontrar a correta
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

echo "<h1>🔍 Teste de URLs de Checkout SumUp</h1>";

// ID de checkout de exemplo (use um real dos logs se tiver)
$checkout_id = "4161d165-9736-4f19-ab80-6cdd8f79870a"; // Do log anterior

echo "<p><strong>Checkout ID de teste:</strong> {$checkout_id}</p>";

// URLs possíveis para testar
$possible_urls = [
    "https://pay.sumup.com/checkout/{$checkout_id}",
    "https://checkout.sumup.com/{$checkout_id}",
    "https://sumup.com/checkout/{$checkout_id}",
    "https://api.sumup.com/checkouts/{$checkout_id}",
    "https://checkout.sumup.com/pay/{$checkout_id}",
    "https://pay.sumup.com/{$checkout_id}",
    "https://sumup.com/pay/{$checkout_id}",
    "https://checkout-sandbox.sumup.com/{$checkout_id}",
    "https://pay-sandbox.sumup.com/checkout/{$checkout_id}"
];

echo "<h2>Testando URLs Possíveis</h2>";

foreach ($possible_urls as $url) {
    echo "<h3>Testando: <a href='{$url}' target='_blank'>{$url}</a></h3>";
    
    // Fazer requisição HEAD para verificar se existe
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p>❌ Erro cURL: {$error}</p>";
    } else {
        echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
        
        if ($final_url !== $url) {
            echo "<p><strong>Redirecionado para:</strong> {$final_url}</p>";
        }
        
        if ($http_code == 200) {
            echo "<p>✅ <strong>SUCESSO!</strong> Esta URL parece funcionar!</p>";
            echo "<p><a href='{$url}' target='_blank' style='background: #4CAF50; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🔗 Abrir URL</a></p>";
        } elseif ($http_code == 404) {
            echo "<p>❌ 404 - Não encontrado</p>";
        } elseif ($http_code == 403) {
            echo "<p>⚠️ 403 - Acesso negado (pode ser válida mas protegida)</p>";
        } elseif ($http_code >= 300 && $http_code < 400) {
            echo "<p>🔄 {$http_code} - Redirecionamento</p>";
        } else {
            echo "<p>⚠️ {$http_code} - Resposta inesperada</p>";
        }
    }
    
    echo "<hr>";
}

echo "<h2>📋 Instruções</h2>";
echo "<ul>";
echo "<li>Clique nas URLs marcadas com ✅ para testar manualmente</li>";
echo "<li>Se alguma URL funcionar, copie o padrão para usar no código</li>";
echo "<li>URLs com 403 podem ser válidas mas precisar de autenticação</li>";
echo "</ul>";

echo "<h2>🔧 Alternativa: Usar Resposta da API</h2>";
echo "<p>Se nenhuma URL funcionar, podemos:</p>";
echo "<ul>";
echo "<li>Verificar se a API SumUp retorna uma URL específica na resposta</li>";
echo "<li>Usar um iframe em vez de abrir nova janela</li>";
echo "<li>Implementar pagamento via API direta</li>";
echo "</ul>";

echo "<h2>📝 Próximos Passos</h2>";
echo "<ol>";
echo "<li>Teste as URLs manualmente clicando nos links</li>";
echo "<li>Se encontrar uma que funciona, me informe</li>";
echo "<li>Vamos atualizar o código com a URL correta</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
