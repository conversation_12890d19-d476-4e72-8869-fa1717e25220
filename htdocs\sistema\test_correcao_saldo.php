<?php
// Script para testar se a correção do saldo final funcionou
// URL: http://localhost:8010/sistema/test_correcao_saldo.php

require_once 'config.php';

echo "<h1>✅ Teste da Correção do Saldo Final</h1>\n";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>📊 Comparação: Antes vs De<PERSON>is da Correção</h2>\n";
    
    // Buscar os últimos 5 registros fechados
    $stmt = $pdo->prepare("
        SELECT 
            r.id,
            r.date as abertura,
            CONCAT(u.first_name, ' ', u.last_name) as usuario,
            r.cash_in_hand as saldo_inicial,
            r.total_cash,
            r.total_reforco,
            r.total_sangrias,
            -- Cálculo ANTIGO (INCORRETO)
            COALESCE(r.total_cash, 0) as saldo_final_antigo,
            (COALESCE(r.total_cash, 0) - COALESCE(r.cash_in_hand, 0) + COALESCE(r.total_sangrias, 0) - COALESCE(r.total_reforco, 0)) as dinheiro_antigo,
            -- Cálculo NOVO (CORRETO)
            (COALESCE(r.cash_in_hand, 0) + (COALESCE(r.total_cash, 0) - COALESCE(r.cash_in_hand, 0)) + COALESCE(r.total_reforco, 0) - COALESCE(r.total_sangrias, 0)) as saldo_final_novo,
            (COALESCE(r.total_cash, 0) - COALESCE(r.cash_in_hand, 0)) as dinheiro_novo
        FROM tec_registers r
        LEFT JOIN tec_users u ON r.user_id = u.id
        WHERE r.status = 'close'
        ORDER BY r.id DESC
        LIMIT 5
    ");
    $stmt->execute();
    $registers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($registers) {
        echo "<div style='overflow-x: auto;'>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 12px;'>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th rowspan='2'>ID</th><th rowspan='2'>Usuário</th><th rowspan='2'>Saldo Inicial</th><th rowspan='2'>Reforço</th><th rowspan='2'>Sangria</th>\n";
        echo "<th colspan='2' style='background: #ffebee;'>ANTES (Incorreto)</th><th colspan='2' style='background: #e8f5e8;'>DEPOIS (Correto)</th>\n";
        echo "</tr>\n";
        echo "<tr style='background: #f0f0f0;'>\n";
        echo "<th style='background: #ffebee;'>Saldo Final</th><th style='background: #ffebee;'>Dinheiro</th>\n";
        echo "<th style='background: #e8f5e8;'>Saldo Final</th><th style='background: #e8f5e8;'>Dinheiro</th>\n";
        echo "</tr>\n";
        
        foreach ($registers as $register) {
            $diferenca_saldo = $register['saldo_final_novo'] - $register['saldo_final_antigo'];
            $diferenca_dinheiro = $register['dinheiro_novo'] - $register['dinheiro_antigo'];
            
            echo "<tr>\n";
            echo "<td>" . $register['id'] . "</td>\n";
            echo "<td>" . $register['usuario'] . "</td>\n";
            echo "<td>R$ " . number_format($register['saldo_inicial'], 2, ',', '.') . "</td>\n";
            echo "<td>R$ " . number_format($register['total_reforco'], 2, ',', '.') . "</td>\n";
            echo "<td>R$ " . number_format($register['total_sangrias'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #ffebee;'>R$ " . number_format($register['saldo_final_antigo'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #ffebee;'>R$ " . number_format($register['dinheiro_antigo'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #e8f5e8;'>R$ " . number_format($register['saldo_final_novo'], 2, ',', '.') . "</td>\n";
            echo "<td style='background: #e8f5e8;'>R$ " . number_format($register['dinheiro_novo'], 2, ',', '.') . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        echo "</div>\n";
    }
    
    echo "<h2>🔍 Explicação da Correção</h2>\n";
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<h3>📝 Fórmula Correta Implementada:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Saldo Final:</strong> Saldo Inicial + Dinheiro (vendas) + Reforço - Sangria</li>\n";
    echo "<li><strong>Dinheiro:</strong> Apenas as vendas em dinheiro (total_cash - cash_in_hand)</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<h3>✅ Alterações Realizadas:</h3>\n";
    echo "<ol>\n";
    echo "<li>Corrigido o cálculo do <strong>Saldo Final</strong> no arquivo <code>app/controllers/Reports.php</code></li>\n";
    echo "<li>Corrigido o cálculo do <strong>Dinheiro</strong> para mostrar apenas vendas em dinheiro</li>\n";
    echo "<li>Mantido o cálculo do <strong>Total de Vendas</strong> correto</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
    echo "<h2>🧪 Teste da Página de Relatórios</h2>\n";
    echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<p><strong>Para verificar se a correção funcionou:</strong></p>\n";
    echo "<ol>\n";
    echo "<li><a href='http://localhost:8010/sistema/reports/registers' target='_blank' style='color: #1976d2; text-decoration: none;'>🔗 Abrir Página de Relatórios de Registros</a></li>\n";
    echo "<li>Verificar se os valores de <strong>Saldo Final</strong> e <strong>Dinheiro</strong> estão corretos</li>\n";
    echo "<li>Comparar com os valores mostrados nesta página de teste</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
    echo "<h2>📋 Resumo da Correção</h2>\n";
    echo "<div style='background: #f3e5f5; padding: 15px; border-radius: 8px; margin: 15px 0;'>\n";
    echo "<h4>Problema Identificado:</h4>\n";
    echo "<ul>\n";
    echo "<li>❌ Saldo Final estava mostrando apenas o <code>total_cash</code></li>\n";
    echo "<li>❌ Dinheiro estava incluindo sangrias e reforços incorretamente</li>\n";
    echo "</ul>\n";
    echo "<h4>Solução Aplicada:</h4>\n";
    echo "<ul>\n";
    echo "<li>✅ Saldo Final agora calcula: <code>Saldo Inicial + Vendas em Dinheiro + Reforço - Sangria</code></li>\n";
    echo "<li>✅ Dinheiro agora mostra apenas: <code>Vendas em Dinheiro (total_cash - cash_in_hand)</code></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (PDOException $e) {
    echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; color: #d32f2f;'>\n";
    echo "<h3>❌ Erro de Conexão:</h3>\n";
    echo "<p>" . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>
