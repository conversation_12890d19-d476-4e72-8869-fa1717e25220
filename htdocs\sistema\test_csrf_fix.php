<?php
/**
 * Teste de Correção CSRF
 * 
 * Este script testa se a correção do CSRF funcionou
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

echo "<h1>🔍 Teste de Correção CSRF</h1>";

echo "<h2>1. Verificação da Configuração CSRF</h2>";

// Verificar se o arquivo de configuração foi modificado
$config_file = 'app/config/config.php';
if (file_exists($config_file)) {
    $config_content = file_get_contents($config_file);

    echo "<p>✅ Arquivo de configuração encontrado</p>";

    // Verificar se as exclusões foram adicionadas
    if (strpos($config_content, 'pos/create_sumup_checkout') !== false) {
        echo "<p>✅ Exclusão 'pos/create_sumup_checkout' encontrada</p>";
    } else {
        echo "<p>❌ Exclusão 'pos/create_sumup_checkout' NÃO encontrada</p>";
    }

    if (strpos($config_content, 'sumup_webhook') !== false) {
        echo "<p>✅ Exclusão 'sumup_webhook' encontrada</p>";
    } else {
        echo "<p>❌ Exclusão 'sumup_webhook' NÃO encontrada</p>";
    }

    // Mostrar a seção csrf_exclude_uris
    if (preg_match('/\$config\[\'csrf_exclude_uris\'\]\s*=\s*array\((.*?)\);/s', $config_content, $matches)) {
        echo "<p><strong>Configuração csrf_exclude_uris:</strong></p>";
        echo "<pre>" . htmlspecialchars($matches[0]) . "</pre>";
    } else {
        echo "<p>❌ Configuração csrf_exclude_uris não encontrada</p>";
    }

} else {
    echo "<p>❌ Arquivo de configuração não encontrado</p>";
}

echo "<h2>2. Teste de Requisição POST (Sem CSRF)</h2>";

$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/pos/create_sumup_checkout';

echo "<p>Testando: {$test_url}</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'amount' => '1.00',
    'description' => 'Teste CSRF Fix'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
$redirect_count = curl_getinfo($ch, CURLINFO_REDIRECT_COUNT);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> " . $http_code . "</p>";
    echo "<p><strong>URL Final:</strong> " . $final_url . "</p>";
    echo "<p><strong>Redirecionamentos:</strong> " . $redirect_count . "</p>";

    if ($http_code == 403) {
        echo "<p>❌ Ainda recebendo erro 403 - CSRF não foi resolvido</p>";
        echo "<p>Verifique se a configuração foi salva corretamente</p>";
    } elseif ($http_code == 200) {
        echo "<p>✅ HTTP 200 - CSRF resolvido!</p>";
    } elseif ($http_code == 302 || $http_code == 303) {
        echo "<p>⚠️ HTTP {$http_code} - Redirecionamento detectado</p>";
        echo "<p>Isso pode indicar:</p>";
        echo "<ul>";
        echo "<li>Redirecionamento para página de login (usuário não autenticado)</li>";
        echo "<li>Redirecionamento por erro de validação</li>";
        echo "<li>Redirecionamento programático no controlador</li>";
        echo "</ul>";
    } else {
        echo "<p>⚠️ HTTP {$http_code} - CSRF resolvido, mas pode haver outro problema</p>";
    }
    
    echo "<p><strong>Resposta:</strong></p>";
    
    // Verificar se é JSON
    $json_data = json_decode($response, true);
    if ($json_data) {
        echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
        
        if (isset($json_data['error']) && $json_data['error']) {
            echo "<p>⚠️ Erro na resposta: " . $json_data['message'] . "</p>";
        } elseif (isset($json_data['success']) && $json_data['success']) {
            echo "<p>✅ Sucesso! Checkout criado</p>";
        }
    } else {
        // Mostrar apenas os primeiros 500 caracteres se for HTML
        $preview = substr($response, 0, 500);
        echo "<pre>" . htmlspecialchars($preview) . "</pre>";
        
        if (strlen($response) > 500) {
            echo "<p><em>... (resposta truncada)</em></p>";
        }
    }
}

echo "<h2>3. Verificar Logs</h2>";

$log_file = 'app/logs/log-' . date('Y-m-d') . '.php';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    
    // Procurar por logs do SumUp
    $lines = explode("\n", $log_content);
    $sumup_lines = [];
    
    foreach ($lines as $line) {
        if (stripos($line, 'sumup') !== false || stripos($line, 'create_sumup_checkout') !== false) {
            $sumup_lines[] = $line;
        }
    }
    
    if (!empty($sumup_lines)) {
        echo "<p>✅ Logs do SumUp encontrados:</p>";
        echo "<pre>";
        foreach ($sumup_lines as $line) {
            echo htmlspecialchars($line) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p>⚠️ Nenhum log específico do SumUp encontrado</p>";
        
        // Mostrar as últimas 5 linhas do log
        $recent_lines = array_slice($lines, -5);
        echo "<p><strong>Últimas entradas do log:</strong></p>";
        echo "<pre>";
        foreach ($recent_lines as $line) {
            if (!empty(trim($line)) && strpos($line, '<?php') === false) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>";
    }
} else {
    echo "<p>❌ Arquivo de log não encontrado</p>";
}

echo "<h2>4. Próximos Passos</h2>";
echo "<ul>";
echo "<li>Se HTTP 200: CSRF resolvido, verificar resposta da API SumUp</li>";
echo "<li>Se HTTP 403: Verificar se a configuração foi salva</li>";
echo "<li>Se outro erro: Investigar logs específicos</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
