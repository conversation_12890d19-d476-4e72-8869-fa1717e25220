<?php
/**
 * Teste Direto dos Endpoints SumUp
 * 
 * Este script testa os endpoints diretamente
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

echo "<h1>🔍 Teste Direto dos Endpoints SumUp</h1>";

// Fazer login primeiro para ter sessão ativa
echo "<h2>1. Fazendo Login</h2>";

$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/';
$cookie_jar = tempnam(sys_get_temp_dir(), 'cookies');

// Login
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . 'login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'username' => 'admin',
    'password' => 'admin'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_jar);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_jar);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$login_response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
curl_close($ch);

if (strpos($final_url, 'dashboard') !== false || strpos($final_url, 'pos') !== false) {
    echo "<p>✅ Login realizado com sucesso!</p>";
    $login_success = true;
} else {
    echo "<p>⚠️ Login pode ter falhado, mas vamos tentar mesmo assim</p>";
    $login_success = true; // Tentar mesmo assim
}

if ($login_success) {
    echo "<h2>2. Testando list_sumup_devices</h2>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $base_url . 'index.php/pos/list_sumup_devices');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_jar);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_jar);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    echo "<p><strong>URL Final:</strong> {$final_url}</p>";
    
    if ($http_code == 200) {
        echo "<p>✅ Endpoint funcionando!</p>";
        $json_data = json_decode($response, true);
        if ($json_data) {
            echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    } elseif ($http_code == 404) {
        echo "<p>❌ 404 - Método não encontrado</p>";
        echo "<p>Possíveis causas:</p>";
        echo "<ul>";
        echo "<li>Método não foi salvo no controlador</li>";
        echo "<li>Cache do CodeIgniter</li>";
        echo "<li>Problema de sintaxe no controlador</li>";
        echo "</ul>";
    } elseif ($http_code == 403) {
        echo "<p>❌ 403 - CSRF ainda ativo</p>";
        echo "<p>A exclusão CSRF pode não ter sido salva</p>";
    } else {
        echo "<p>⚠️ HTTP {$http_code}</p>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    }
    
    echo "<h2>3. Testando create_sumup_card_payment</h2>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $base_url . 'index.php/pos/create_sumup_card_payment');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'amount' => '5.00',
        'description' => 'Teste Direto'
    ]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_jar);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_jar);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    echo "<p><strong>URL Final:</strong> {$final_url}</p>";
    
    if ($http_code == 200) {
        echo "<p>✅ Endpoint funcionando!</p>";
        $json_data = json_decode($response, true);
        if ($json_data) {
            echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    } else {
        echo "<p>❌ HTTP {$http_code}</p>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    }
}

// Limpar cookies
if (file_exists($cookie_jar)) {
    unlink($cookie_jar);
}

echo "<h2>4. Verificação de Sintaxe</h2>";

// Verificar se há erro de sintaxe no controlador
$syntax_check = shell_exec('php -l app/controllers/Pos.php 2>&1');
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "<p>✅ Sintaxe do controlador OK</p>";
} else {
    echo "<p>❌ Erro de sintaxe no controlador:</p>";
    echo "<pre>" . htmlspecialchars($syntax_check) . "</pre>";
}

echo "<h2>5. Próximos Passos</h2>";
echo "<ul>";
echo "<li>Se 404: Verificar se métodos estão salvos no controlador</li>";
echo "<li>Se 403: Verificar se CSRF foi salvo na configuração</li>";
echo "<li>Se 200: Endpoints funcionando, problema era no teste anterior</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
