<?php
/**
 * Teste de Carregamento de Modelo via CodeIgniter
 * 
 * Este script simula o carregamento do modelo SumUp via CodeIgniter
 */

// Definir constantes do CodeIgniter
define('BASEPATH', __DIR__ . '/lib/');
define('APPPATH', __DIR__ . '/app/');
define('ENVIRONMENT', 'development');

// Capturar todos os erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Teste de Carregamento de Modelo via CodeIgniter</h1>";

echo "<h2>1. Verificar Estrutura do CodeIgniter</h2>";

$required_files = [
    'lib/core/CodeIgniter.php' => 'Core do CodeIgniter',
    'lib/core/Model.php' => 'Classe Model base',
    'lib/database/DB.php' => 'Database',
    'app/models/Sumup_model.php' => 'Modelo SumUp'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p>✅ {$description}: {$file}</p>";
    } else {
        echo "<p>❌ {$description} não encontrado: {$file}</p>";
    }
}

echo "<h2>2. Tentar Carregar Classes Base do CodeIgniter</h2>";

try {
    // Incluir arquivos base do CodeIgniter
    if (file_exists('lib/core/Common.php')) {
        require_once 'lib/core/Common.php';
        echo "<p>✅ Common.php carregado</p>";
    }
    
    if (file_exists('lib/core/Model.php')) {
        require_once 'lib/core/Model.php';
        echo "<p>✅ Model.php carregado</p>";
    } elseif (file_exists('lib/database/DB.php')) {
        // Tentar carregar CI_Model de outro local
        require_once 'lib/database/DB.php';
        echo "<p>✅ DB.php carregado</p>";
    }
    
    // Verificar se CI_Model existe
    if (class_exists('CI_Model')) {
        echo "<p>✅ Classe CI_Model encontrada</p>";
    } else {
        echo "<p>❌ Classe CI_Model não encontrada</p>";
        
        // Listar classes disponíveis
        $classes = get_declared_classes();
        $ci_classes = array_filter($classes, function($class) {
            return strpos($class, 'CI_') === 0;
        });
        
        if (!empty($ci_classes)) {
            echo "<p>Classes CI_ disponíveis:</p>";
            echo "<ul>";
            foreach ($ci_classes as $class) {
                echo "<li>{$class}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>Nenhuma classe CI_ encontrada</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro ao carregar classes base: " . $e->getMessage() . "</p>";
}

echo "<h2>3. Verificar Estrutura de Diretórios</h2>";

$dirs = [
    'lib/',
    'lib/core/',
    'lib/database/',
    'app/',
    'app/models/',
    'app/controllers/'
];

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        $files = scandir($dir);
        $files = array_filter($files, function($file) {
            return $file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'php';
        });
        
        echo "<p>✅ {$dir} (" . count($files) . " arquivos PHP)</p>";
        
        if ($dir === 'lib/core/') {
            echo "<ul>";
            foreach ($files as $file) {
                echo "<li>{$file}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>❌ Diretório não encontrado: {$dir}</p>";
    }
}

echo "<h2>4. Teste de Requisição Real</h2>";

// Fazer uma requisição real para o controlador POS
$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/pos/create_sumup_checkout';

echo "<p>Testando: {$test_url}</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'amount' => '1.00',
    'description' => 'Teste'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> " . $http_code . "</p>";
    echo "<p><strong>Resposta:</strong></p>";
    
    // Verificar se é JSON
    $json_data = json_decode($response, true);
    if ($json_data) {
        echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
    } else {
        // Mostrar apenas os primeiros 1000 caracteres se for HTML
        $preview = substr($response, 0, 1000);
        echo "<pre>" . htmlspecialchars($preview) . "</pre>";
        
        if (strlen($response) > 1000) {
            echo "<p><em>... (resposta truncada)</em></p>";
        }
    }
}

echo "<h2>5. Verificar Logs Mais Recentes</h2>";

$log_file = 'app/logs/log-' . date('Y-m-d') . '.php';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    // Pegar as últimas 10 linhas
    $recent_lines = array_slice($lines, -10);
    
    echo "<p><strong>Últimas entradas do log:</strong></p>";
    echo "<pre>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line)) && strpos($line, '<?php') === false) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p>❌ Arquivo de log não encontrado</p>";
}

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
