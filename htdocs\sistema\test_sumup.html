<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste SumUp API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        input[type="number"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100px;
            margin: 0 10px;
        }
        
        .link-result {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        
        .link-result a {
            color: #007bff;
            text-decoration: none;
            word-break: break-all;
        }
        
        .link-result a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste da API SumUp</h1>
        
        <div class="test-section">
            <h3>1. Teste de Configuração</h3>
            <p>Verifica se a API SumUp está configurada corretamente.</p>
            <button onclick="testConfig()">🔧 Testar Configuração</button>
            <div id="configResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Teste de Criação de Checkout</h3>
            <p>Testa a criação de um link de pagamento. A SumUp automaticamente oferece todas as opções disponíveis.</p>
            <label>
                Valor: R$ <input type="number" id="testAmount" value="100.00" step="0.01" min="0.01">
            </label>
            <br><br>
            <div style="margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 5px;">
                <strong>✅ Opções Automáticas da SumUp:</strong><br>
                • 💳 Cartão de Crédito (parcelamento automático)<br>
                • 💳 Cartão de Débito<br>
                • 🏦 PIX (se disponível na conta)<br>
                • 🔒 3D Secure para segurança<br>
                • 📱 Interface responsiva
            </div>
            <button onclick="testCheckout()">💳 Criar Link de Pagamento</button>
            <div id="checkoutResult" class="result" style="display: none;"></div>
            <div id="paymentLink" class="link-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Teste Completo do PDV</h3>
            <p>Simula o fluxo completo do sistema PDV. A SumUp oferece automaticamente todas as opções de pagamento disponíveis para sua conta.</p>
            <label>
                Valor: R$ <input type="number" id="pdvAmount" value="199.90" step="0.01" min="0.01">
            </label>
            <br><br>
            <div style="margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 5px;">
                <strong>🎯 Funcionalidades Reais da SumUp:</strong><br>
                • 💳 <strong>Cartão de Crédito</strong> - Parcelamento automático conforme configuração da conta<br>
                • 💳 <strong>Cartão de Débito</strong> - Débito à vista<br>
                • 🏦 <strong>PIX</strong> - Se habilitado na sua conta SumUp<br>
                • 🔒 <strong>3D Secure</strong> - Autenticação segura automática<br>
                • 📱 <strong>Mobile First</strong> - Interface otimizada para celular<br>
                • 🌍 <strong>Multi-idioma</strong> - Português, inglês, etc.
            </div>
            <button onclick="testPDVFlow()">🏪 Simular PDV Completo</button>
            <div id="pdvResult" class="result" style="display: none;"></div>
            <div id="pdvLink" class="link-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 Informações do Sistema</h3>
            <p><strong>Merchant Code:</strong> MVTC6RDA</p>
            <p><strong>Ambiente:</strong> Produção</p>
            <p><strong>API URL:</strong> https://api.sumup.com</p>
            <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
            <br>
            <button onclick="window.open('sumup_payment_options_info.html', '_blank')" style="background: #17a2b8;">
                📖 Ver Opções de Pagamento Detalhadas
            </button>
        </div>
    </div>

    <script>
        // Atualizar timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString('pt-BR');
        
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = 'result ' + type;
            element.textContent = content;
            element.style.display = 'block';
        }
        
        function showLoading(elementId, message = 'Carregando...') {
            showResult(elementId, message, 'loading');
        }
        
        function testConfig() {
            showLoading('configResult', '🔄 Testando configuração...');
            
            fetch('pos/test_sumup_api')
                .then(response => response.json())
                .then(data => {
                    const resultText = JSON.stringify(data, null, 2);
                    const resultType = data.success ? 'success' : 'error';
                    showResult('configResult', resultText, resultType);
                })
                .catch(error => {
                    showResult('configResult', 'Erro de rede: ' + error.message, 'error');
                });
        }
        
        function testCheckout() {
            const amount = document.getElementById('testAmount').value;
            showLoading('checkoutResult', '🔄 Criando checkout...');
            document.getElementById('paymentLink').style.display = 'none';
            
            fetch('pos/test_sumup_api')
                .then(response => response.json())
                .then(data => {
                    const resultText = JSON.stringify(data, null, 2);
                    const resultType = data.success ? 'success' : 'error';
                    showResult('checkoutResult', resultText, resultType);
                    
                    // Mostrar link se disponível
                    if (data.success && data.api_response && data.api_response.data) {
                        const linkElement = document.getElementById('paymentLink');
                        const hostedUrl = data.api_response.data.hosted_checkout_url;
                        const checkoutId = data.api_response.data.id;

                        if (hostedUrl) {
                            // URL direta da SumUp (correto)
                            linkElement.innerHTML = `
                                <div style="background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;">
                                    <strong>✅ Link CORRETO da SumUp:</strong><br>
                                    <a href="${hostedUrl}" target="_blank" style="word-break: break-all;">
                                        ${hostedUrl}
                                    </a>
                                    <br><br>
                                    <button onclick="window.open('${hostedUrl}', '_blank')" style="background: #28a745;">
                                        🌐 Abrir Página da SumUp
                                    </button>
                                </div>
                            `;
                        } else {
                            // URL interna (incorreto)
                            const internalUrl = 'http://localhost:8010/sistema/sumup/process/' + checkoutId;
                            linkElement.innerHTML = `
                                <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb;">
                                    <strong>❌ URL Interna (INCORRETA):</strong><br>
                                    <a href="${internalUrl}" target="_blank" style="word-break: break-all;">
                                        ${internalUrl}
                                    </a>
                                    <br><br>
                                    <p><strong>Problema:</strong> hosted_checkout_url não foi retornada pela API</p>
                                </div>
                            `;
                        }
                        linkElement.style.display = 'block';
                    }
                })
                .catch(error => {
                    showResult('checkoutResult', 'Erro de rede: ' + error.message, 'error');
                });
        }
        
        function testPDVFlow() {
            const amount = document.getElementById('pdvAmount').value;
            showLoading('pdvResult', '🔄 Simulando fluxo do PDV...');
            document.getElementById('pdvLink').style.display = 'none';
            
            const formData = new FormData();
            formData.append('amount', amount);
            formData.append('description', 'Teste PDV - ' + new Date().toLocaleString('pt-BR'));
            
            fetch('pos/create_sumup_card_payment', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    const resultText = JSON.stringify(data, null, 2);
                    const resultType = data.success ? 'success' : 'error';
                    showResult('pdvResult', resultText, resultType);
                    
                    // Mostrar link se disponível
                    if (data.success && data.checkout_url) {
                        const linkElement = document.getElementById('pdvLink');
                        const isSumUpUrl = data.checkout_url.includes('checkout.sumup.com');

                        if (isSumUpUrl) {
                            // URL da SumUp (correto)
                            linkElement.innerHTML = `
                                <div style="background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;">
                                    <strong>✅ Link CORRETO da SumUp:</strong><br>
                                    <a href="${data.checkout_url}" target="_blank" style="word-break: break-all;">
                                        ${data.checkout_url}
                                    </a>
                                    <br><br>
                                    <button onclick="window.open('${data.checkout_url}', '_blank')" style="background: #28a745;">
                                        💳 Abrir Página da SumUp
                                    </button>
                                    <button onclick="openPaymentLinkPage('${amount}', '${data.checkout_url}', '${data.checkout_id}')" style="background: #17a2b8;">
                                        📱 Abrir Interface de Link
                                    </button>
                                </div>
                            `;
                        } else {
                            // URL interna (incorreto)
                            linkElement.innerHTML = `
                                <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb;">
                                    <strong>❌ URL Interna (INCORRETA):</strong><br>
                                    <a href="${data.checkout_url}" target="_blank" style="word-break: break-all;">
                                        ${data.checkout_url}
                                    </a>
                                    <br><br>
                                    <p><strong>Problema:</strong> Sistema está usando URL interna em vez da hosted_checkout_url da SumUp</p>
                                    <button onclick="window.open('${data.checkout_url}', '_blank')" style="background: #dc3545;">
                                        ⚠️ Abrir URL Interna (Não Funciona)
                                    </button>
                                </div>
                            `;
                        }
                        linkElement.style.display = 'block';
                    }
                })
                .catch(error => {
                    showResult('pdvResult', 'Erro de rede: ' + error.message, 'error');
                });
        }
        
        function openPaymentLinkPage(amount, url, checkoutId) {
            const description = prompt('📝 Digite a descrição do pagamento:', 'Teste de pagamento - ' + new Date().toLocaleString('pt-BR'));

            const linkPageUrl = 'sumup_payment_link.html?' +
                'amount=' + encodeURIComponent(amount) +
                '&url=' + encodeURIComponent(url) +
                '&checkout_id=' + encodeURIComponent(checkoutId) +
                '&description=' + encodeURIComponent(description || 'Teste de pagamento');

            window.open(linkPageUrl, 'sumup_payment_link', 'width=600,height=800,scrollbars=yes,resizable=yes');
        }
    </script>
</body>
</html>
