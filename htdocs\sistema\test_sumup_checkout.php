<?php
/**
 * Teste Direto - Criação de Checkout SumUp
 * 
 * Este arquivo testa diretamente a criação de checkout sem usar o framework CodeIgniter
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

// Incluir configurações
require_once 'config.php';

// Definir header JSON
header('Content-Type: application/json');

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => true, 'message' => 'Método não permitido']);
    exit;
}

// Obter parâmetros
$amount = $_POST['amount'] ?? 0;
$description = $_POST['description'] ?? 'Teste de Checkout';

if (!$amount || $amount <= 0) {
    echo json_encode(['error' => true, 'message' => 'Valor inválido']);
    exit;
}

// Verificar configurações
if (empty($SUMUP_API_KEY) && (empty($SUMUP_CLIENT_ID) || empty($SUMUP_CLIENT_SECRET))) {
    echo json_encode(['error' => true, 'message' => 'SumUp não está configurado']);
    exit;
}

if (empty($SUMUP_MERCHANT_CODE) && empty($SUMUP_PAY_TO_EMAIL)) {
    echo json_encode(['error' => true, 'message' => 'SUMUP_MERCHANT_CODE ou SUMUP_PAY_TO_EMAIL deve ser configurado']);
    exit;
}

/**
 * Função para obter token de acesso
 */
function getAccessToken($api_key, $client_id, $client_secret, $api_url) {
    // Se temos uma chave privada, usar ela
    if (!empty($api_key) && strpos($api_key, 'sup_sk_') === 0) {
        return $api_key;
    }
    
    // Se não temos client_id e client_secret, não podemos obter token
    if (empty($client_id) || empty($client_secret)) {
        return null;
    }
    
    // Fazer requisição OAuth
    $auth_url = $api_url . '/token';
    $auth_data = [
        'grant_type' => 'client_credentials',
        'client_id' => $client_id,
        'client_secret' => $client_secret,
        'scope' => 'payments'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $auth_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($auth_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        $token_data = json_decode($response, true);
        if (isset($token_data['access_token'])) {
            return $token_data['access_token'];
        }
    }
    
    return null;
}

/**
 * Função para fazer requisição à API
 */
function makeRequest($url, $method, $data, $headers) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => true, 'message' => 'cURL Error: ' . $error];
    }
    
    $decoded_response = json_decode($response, true);
    
    return [
        'success' => $http_code >= 200 && $http_code < 300,
        'http_code' => $http_code,
        'data' => $decoded_response,
        'raw_response' => $response
    ];
}

try {
    // Obter token de acesso
    $access_token = getAccessToken($SUMUP_API_KEY, $SUMUP_CLIENT_ID, $SUMUP_CLIENT_SECRET, $SUMUP_API_URL);
    
    if (!$access_token) {
        echo json_encode(['error' => true, 'message' => 'Não foi possível obter token de acesso']);
        exit;
    }
    
    // Preparar dados do checkout
    $checkout_data = [
        'checkout_reference' => 'test_' . uniqid(),
        'amount' => floatval($amount),
        'currency' => 'BRL',
        'description' => $description,
        'return_url' => 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/pos/sumup_callback'
    ];

    // Adicionar merchant_code OU pay_to_email
    if (!empty($SUMUP_MERCHANT_CODE)) {
        $checkout_data['merchant_code'] = $SUMUP_MERCHANT_CODE;
    } elseif (!empty($SUMUP_PAY_TO_EMAIL)) {
        $checkout_data['pay_to_email'] = $SUMUP_PAY_TO_EMAIL;
    }

    // Debug: mostrar dados que serão enviados
    error_log('SumUp Checkout Data: ' . json_encode($checkout_data));
    
    // Fazer requisição para criar checkout
    $result = makeRequest(
        $SUMUP_API_URL . '/v0.1/checkouts',
        'POST',
        $checkout_data,
        [
            'Authorization: Bearer ' . $access_token,
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    );
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'checkout_id' => $result['data']['id'],
            'checkout_url' => $SUMUP_API_URL . '/v0.1/checkouts/' . $result['data']['id'],
            'data' => $result['data']
        ]);
    } else {
        echo json_encode([
            'error' => true,
            'message' => 'Erro ao criar checkout: ' . ($result['data']['message'] ?? 'Erro desconhecido'),
            'http_code' => $result['http_code'],
            'raw_response' => $result['raw_response']
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'message' => 'Exceção: ' . $e->getMessage()
    ]);
}
?>
