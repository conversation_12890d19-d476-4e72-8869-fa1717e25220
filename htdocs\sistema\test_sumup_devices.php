<?php
/**
 * Teste de Máquinas SumUp
 * 
 * Este script verifica se há máquinas SumUp registradas na conta
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

echo "<h1>🔍 Teste de Máquinas SumUp</h1>";

echo "<h2>1. Verificar Máquinas Registradas</h2>";

$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/pos/list_sumup_devices';

echo "<p>Testando: {$test_url}</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    
    if ($http_code == 200) {
        $json_data = json_decode($response, true);
        if ($json_data) {
            echo "<p><strong>Resposta JSON:</strong></p>";
            echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
            
            if (isset($json_data['success']) && $json_data['success']) {
                $device_count = $json_data['count'] ?? 0;
                echo "<p>✅ <strong>{$device_count} máquina(s) encontrada(s)</strong></p>";
                
                if ($device_count > 0) {
                    echo "<h3>Máquinas Disponíveis:</h3>";
                    echo "<ul>";
                    foreach ($json_data['devices'] as $device) {
                        echo "<li>";
                        echo "<strong>ID:</strong> " . ($device['id'] ?? 'N/A') . "<br>";
                        echo "<strong>Nome:</strong> " . ($device['name'] ?? 'N/A') . "<br>";
                        echo "<strong>Tipo:</strong> " . ($device['type'] ?? 'N/A') . "<br>";
                        echo "<strong>Status:</strong> " . ($device['status'] ?? 'N/A') . "<br>";
                        echo "</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p>⚠️ Nenhuma máquina registrada na conta</p>";
                }
            } else {
                echo "<p>❌ Erro: " . ($json_data['message'] ?? 'Erro desconhecido') . "</p>";
            }
        } else {
            echo "<p>❌ Resposta não é JSON válido:</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p>❌ Erro HTTP: {$http_code}</p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

echo "<h2>2. Teste de Pagamento na Máquina</h2>";

$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/pos/create_sumup_card_payment';

echo "<p>Testando: {$test_url}</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'amount' => '5.00',
    'description' => 'Teste Máquina SumUp'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
    
    if ($http_code == 200) {
        $json_data = json_decode($response, true);
        if ($json_data) {
            echo "<p><strong>Resposta JSON:</strong></p>";
            echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
            
            if (isset($json_data['success']) && $json_data['success']) {
                echo "<p>✅ <strong>Pagamento enviado para a máquina!</strong></p>";
                echo "<p>🔔 Verifique o visor da máquina SumUp</p>";
            } else {
                echo "<p>❌ Erro: " . ($json_data['message'] ?? 'Erro desconhecido') . "</p>";
            }
        } else {
            echo "<p>❌ Resposta não é JSON válido:</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    } else {
        echo "<p>❌ Erro HTTP: {$http_code}</p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

echo "<h2>📋 Instruções</h2>";
echo "<ul>";
echo "<li><strong>Se não há máquinas:</strong> Você precisa registrar uma máquina SumUp na sua conta</li>";
echo "<li><strong>Se há máquinas:</strong> O pagamento deve aparecer no visor da máquina</li>";
echo "<li><strong>Para registrar máquina:</strong> Use o app SumUp no celular para parear</li>";
echo "</ul>";

echo "<h2>🔧 Próximos Passos</h2>";
echo "<ol>";
echo "<li>Se há máquinas registradas, modifique o PDV para usar pagamento na máquina</li>";
echo "<li>Se não há máquinas, registre uma máquina SumUp primeiro</li>";
echo "<li>Teste o pagamento físico na máquina</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
