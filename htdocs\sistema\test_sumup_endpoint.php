<?php
/**
 * Teste Direto do Endpoint SumUp
 * 
 * Este script testa diretamente o endpoint create_sumup_checkout
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

// Capturar todos os erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Teste Direto do Endpoint SumUp</h1>";

echo "<h2>1. Teste GET (deve dar erro de método)</h2>";

$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/pos/create_sumup_checkout';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> " . $http_code . "</p>";
    echo "<p><strong>Resposta:</strong></p>";
    echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "...</pre>";
}

echo "<h2>2. Teste POST (deve tentar criar checkout)</h2>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'amount' => '10.50',
    'description' => 'Teste Endpoint'
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'X-Requested-With: XMLHttpRequest'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} else {
    echo "<p><strong>HTTP Code:</strong> " . $http_code . "</p>";
    echo "<p><strong>Resposta:</strong></p>";
    
    // Tentar decodificar como JSON
    $json_data = json_decode($response, true);
    if ($json_data) {
        echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
    } else {
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

echo "<h2>3. Verificar Logs Após Teste</h2>";

// Verificar se há logs novos
$log_file = 'app/logs/log-' . date('Y-m-d') . '.php';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    // Pegar as últimas 20 linhas
    $recent_lines = array_slice($lines, -20);
    
    echo "<p><strong>Últimas entradas do log:</strong></p>";
    echo "<pre>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line)) && strpos($line, '<?php') === false) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
    
    // Procurar por SumUp especificamente
    $sumup_lines = [];
    foreach ($lines as $line) {
        if (stripos($line, 'sumup') !== false) {
            $sumup_lines[] = $line;
        }
    }
    
    if (!empty($sumup_lines)) {
        echo "<p><strong>Entradas relacionadas ao SumUp:</strong></p>";
        echo "<pre>";
        foreach ($sumup_lines as $line) {
            echo htmlspecialchars($line) . "\n";
        }
        echo "</pre>";
    } else {
        echo "<p>⚠️ Nenhuma entrada relacionada ao SumUp encontrada nos logs</p>";
    }
    
} else {
    echo "<p>❌ Arquivo de log não encontrado: " . $log_file . "</p>";
}

echo "<h2>4. Análise</h2>";
echo "<ul>";
echo "<li>Se o endpoint retornar JSON com erro, o modelo está sendo chamado</li>";
echo "<li>Se retornar HTML ou erro 404, há problema no roteamento</li>";
echo "<li>Se não houver logs do SumUp, o modelo não está sendo executado</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
