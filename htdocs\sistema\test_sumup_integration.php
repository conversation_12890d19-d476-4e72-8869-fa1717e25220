<?php
/**
 * Script de Teste - Integração SumUp
 * 
 * Este script testa a integração com a API da SumUp
 * Execute via navegador: http://seudominio.com/sistema/test_sumup_integration.php
 */

// Incluir configurações
require_once 'config.php';

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Teste Integração SumUp</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Teste de Integração SumUp</h1>
    
    <div class="test-section">
        <h2>1. Verificação de Configurações</h2>
        <?php
        echo "<p><strong>Ambiente:</strong> " . (isset($SUMUP_SANDBOX) && $SUMUP_SANDBOX ? "Sandbox" : "Produção") . "</p>";
        
        $config_ok = true;
        
        if (empty($SUMUP_API_KEY)) {
            echo "<p class='error'>❌ SUMUP_API_KEY não configurado</p>";
            $config_ok = false;
        } else {
            echo "<p class='success'>✅ SUMUP_API_KEY configurado</p>";
        }
        
        if (empty($SUMUP_MERCHANT_CODE)) {
            echo "<p class='error'>❌ SUMUP_MERCHANT_CODE não configurado</p>";
            $config_ok = false;
        } else {
            echo "<p class='success'>✅ SUMUP_MERCHANT_CODE configurado</p>";
        }
        
        if (!extension_loaded('curl')) {
            echo "<p class='error'>❌ Extensão cURL não está habilitada</p>";
            $config_ok = false;
        } else {
            echo "<p class='success'>✅ Extensão cURL habilitada</p>";
        }
        
        if ($config_ok) {
            echo "<p class='success'><strong>✅ Configurações OK</strong></p>";
        } else {
            echo "<p class='error'><strong>❌ Configurações incompletas</strong></p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>2. Teste de Conectividade</h2>
        <?php
        if ($config_ok) {
            // Primeiro, verificar se temos uma chave privada ou precisamos fazer OAuth
            $api_url = isset($SUMUP_API_URL) ? $SUMUP_API_URL : 'https://api.sumup.com';
            $access_token = null;

            if (strpos($SUMUP_API_KEY, 'sup_sk_') === 0) {
                // Chave privada - usar diretamente
                $access_token = $SUMUP_API_KEY;
                echo "<p class='success'>✅ Usando chave privada (sup_sk_)</p>";
            } elseif (!empty($SUMUP_CLIENT_ID) && !empty($SUMUP_CLIENT_SECRET)) {
                // Fazer OAuth para obter token
                echo "<p class='warning'>⚠️ Chave pública detectada, tentando OAuth...</p>";

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url . '/token');
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
                    'grant_type' => 'client_credentials',
                    'client_id' => $SUMUP_CLIENT_ID,
                    'client_secret' => $SUMUP_CLIENT_SECRET,
                    'scope' => 'payments'
                ]));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/x-www-form-urlencoded'
                ]);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);

                $oauth_response = curl_exec($ch);
                $oauth_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($oauth_http_code === 200) {
                    $token_data = json_decode($oauth_response, true);
                    if (isset($token_data['access_token'])) {
                        $access_token = $token_data['access_token'];
                        echo "<p class='success'>✅ Token OAuth obtido com sucesso</p>";
                    } else {
                        echo "<p class='error'>❌ Resposta OAuth inválida</p>";
                        echo "<pre>" . $oauth_response . "</pre>";
                    }
                } else {
                    echo "<p class='error'>❌ Erro OAuth: " . $oauth_http_code . "</p>";
                    echo "<pre>" . $oauth_response . "</pre>";
                }
            } else {
                echo "<p class='error'>❌ Você precisa de uma chave privada (sup_sk_) OU client_id + client_secret</p>";
                echo "<p><strong>Opção 1:</strong> Use uma chave privada no SUMUP_API_KEY</p>";
                echo "<p><strong>Opção 2:</strong> Preencha SUMUP_CLIENT_ID e SUMUP_CLIENT_SECRET</p>";
            }

            // Testar API se temos token
            if ($access_token) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url . '/v0.1/me');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $access_token,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);

                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);

                if ($error) {
                    echo "<p class='error'>❌ Erro cURL: " . $error . "</p>";
                } elseif ($http_code == 200) {
                    echo "<p class='success'>✅ Conexão com API SumUp OK</p>";
                    $data = json_decode($response, true);
                    if ($data) {
                        echo "<p><strong>Informações da conta:</strong></p>";
                        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";

                        // Testar merchant profile se temos merchant_code
                        if (!empty($SUMUP_MERCHANT_CODE)) {
                            echo "<h4>Testando Merchant Profile:</h4>";

                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, $api_url . '/v0.1/me/merchant-profile');
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                'Authorization: Bearer ' . $access_token,
                                'Content-Type: application/json'
                            ]);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            curl_setopt($ch, CURLOPT_TIMEOUT, 10);

                            $merchant_response = curl_exec($ch);
                            $merchant_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            curl_close($ch);

                            if ($merchant_http_code == 200) {
                                $merchant_data = json_decode($merchant_response, true);
                                echo "<p class='success'>✅ Merchant Profile OK</p>";
                                echo "<pre>" . json_encode($merchant_data, JSON_PRETTY_PRINT) . "</pre>";

                                // Verificar se o merchant_code configurado está correto
                                if (isset($merchant_data['merchant_code']) && $merchant_data['merchant_code'] !== $SUMUP_MERCHANT_CODE) {
                                    echo "<p class='warning'>⚠️ ATENÇÃO: Merchant code configurado ({$SUMUP_MERCHANT_CODE}) é diferente do retornado pela API ({$merchant_data['merchant_code']})</p>";
                                }
                            } else {
                                echo "<p class='error'>❌ Erro ao obter merchant profile: " . $merchant_http_code . "</p>";
                                echo "<pre>" . $merchant_response . "</pre>";
                            }
                        }
                    }
                } else {
                    echo "<p class='error'>❌ Erro HTTP: " . $http_code . "</p>";
                    echo "<p>Resposta: " . $response . "</p>";
                }
            }
        } else {
            echo "<p class='warning'>⚠️ Pule este teste - configurações incompletas</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>3. Teste de Criação de Checkout</h2>
        <button onclick="testCreateCheckout()">Criar Checkout de Teste (CodeIgniter)</button>
        <button onclick="testCreateCheckoutDirect()">Criar Checkout de Teste (Direto)</button>
        <div id="checkout-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Verificação do Banco de Dados</h2>
        <?php
        try {
            $pdo = new PDO("mysql:host=" . str_replace(':33306', '', $PDV_HOST) . ";port=33306;dbname=" . $PDV_BASE, $PDV_USUARIO, $PDV_SENHA);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Verificar se SumUp está na tabela de métodos de pagamento
            $stmt = $pdo->prepare("SELECT * FROM tec_meiopagamento WHERE cod = 'sumup'");
            $stmt->execute();
            $sumup_method = $stmt->fetch();
            
            if ($sumup_method) {
                echo "<p class='success'>✅ SumUp encontrado na tabela de métodos de pagamento</p>";
                echo "<pre>" . json_encode($sumup_method, JSON_PRETTY_PRINT) . "</pre>";
            } else {
                echo "<p class='error'>❌ SumUp não encontrado na tabela de métodos de pagamento</p>";
                echo "<p>Execute o script SQL: <code>install/add_sumup_payment_method.sql</code></p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Erro de conexão com banco de dados: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>5. Verificação de Arquivos</h2>
        <?php
        $required_files = [
            'app/models/Sumup_model.php' => 'Modelo SumUp',
            'app/controllers/Sumup_webhook.php' => 'Controlador de Webhooks',
            'install/add_sumup_payment_method.sql' => 'Script SQL'
        ];
        
        foreach ($required_files as $file => $description) {
            if (file_exists($file)) {
                echo "<p class='success'>✅ {$description}: {$file}</p>";
            } else {
                echo "<p class='error'>❌ {$description} não encontrado: {$file}</p>";
            }
        }
        ?>
    </div>

    <div class="test-section">
        <h2>6. URLs de Teste</h2>
        <ul>
            <li><a href="pos" target="_blank">PDV (testar pagamento SumUp)</a></li>
            <li><a href="sumup_webhook/test" target="_blank">Teste de Webhook</a></li>
        </ul>
    </div>

    <script>
    function testCreateCheckout() {
        const resultDiv = document.getElementById('checkout-result');
        resultDiv.innerHTML = '<p>Criando checkout de teste...</p>';
        
        fetch('index.php/pos/create_sumup_checkout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'amount=10.50&description=Teste de Checkout'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.text().then(text => {
                console.log('Response text:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error(`Resposta não é JSON válido: ${text.substring(0, 200)}...`);
                }
            });
        })
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <p class="success">✅ Checkout criado com sucesso!</p>
                    <p><strong>ID:</strong> ${data.checkout_id}</p>
                    <p><strong>URL:</strong> <a href="${data.checkout_url}" target="_blank">${data.checkout_url}</a></p>
                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `<p class="error">❌ Erro: ${data.message}</p>`;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultDiv.innerHTML = `<p class="error">❌ Erro: ${error.message}</p>`;
        });
    }

    function testCreateCheckoutDirect() {
        const resultDiv = document.getElementById('checkout-result');
        resultDiv.innerHTML = '<p>Criando checkout de teste (direto)...</p>';

        fetch('test_sumup_checkout.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'amount=10.50&description=Teste de Checkout Direto'
        })
        .then(response => {
            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.text().then(text => {
                console.log('Response text:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error(`Resposta não é JSON válido: ${text.substring(0, 200)}...`);
                }
            });
        })
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <p class="success">✅ Checkout criado com sucesso!</p>
                    <p><strong>ID:</strong> ${data.checkout_id}</p>
                    <p><strong>URL:</strong> <a href="${data.checkout_url}" target="_blank">${data.checkout_url}</a></p>
                    <pre>${JSON.stringify(data.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <p class="error">❌ Erro: ${data.message}</p>
                    ${data.http_code ? `<p><strong>HTTP Code:</strong> ${data.http_code}</p>` : ''}
                    ${data.raw_response ? `<p><strong>Resposta:</strong></p><pre>${data.raw_response}</pre>` : ''}
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultDiv.innerHTML = `<p class="error">❌ Erro: ${error.message}</p>`;
        });
    }
    </script>

    <hr>
    <p><small>
        <strong>Nota:</strong> Este script deve ser removido em produção.<br>
        <strong>Data:</strong> <?php echo date('d/m/Y H:i:s'); ?>
    </small></p>
</body>
</html>
