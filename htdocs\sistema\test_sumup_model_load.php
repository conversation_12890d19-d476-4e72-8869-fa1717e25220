<?php
/**
 * Teste de Carregamento do Modelo SumUp
 * 
 * Este script testa se o modelo SumUp está sendo carregado corretamente
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

// Capturar todos os erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Teste de Carregamento do Modelo SumUp</h1>";

// Incluir configurações
require_once 'config.php';

echo "<h2>1. Configurações</h2>";
echo "<ul>";
echo "<li><strong>SUMUP_API_KEY:</strong> " . (empty($SUMUP_API_KEY) ? "❌ Vazio" : "✅ " . substr($SUMUP_API_KEY, 0, 15) . "...") . "</li>";
echo "<li><strong>SUMUP_PAY_TO_EMAIL:</strong> " . (empty($SUMUP_PAY_TO_EMAIL) ? "❌ Vazio" : "✅ " . $SUMUP_PAY_TO_EMAIL) . "</li>";
echo "</ul>";

echo "<h2>2. Teste de Arquivo</h2>";
if (file_exists('app/models/Sumup_model.php')) {
    echo "<p>✅ Arquivo Sumup_model.php encontrado</p>";
    echo "<p><strong>Tamanho:</strong> " . filesize('app/models/Sumup_model.php') . " bytes</p>";
    echo "<p><strong>Modificado:</strong> " . date('d/m/Y H:i:s', filemtime('app/models/Sumup_model.php')) . "</p>";
} else {
    echo "<p>❌ Arquivo Sumup_model.php NÃO encontrado</p>";
    exit;
}

echo "<h2>3. Teste de Sintaxe PHP</h2>";
$syntax_check = shell_exec('php -l app/models/Sumup_model.php 2>&1');
if (strpos($syntax_check, 'No syntax errors') !== false) {
    echo "<p>✅ Sintaxe PHP OK</p>";
} else {
    echo "<p>❌ Erro de sintaxe:</p>";
    echo "<pre>" . htmlspecialchars($syntax_check) . "</pre>";
    exit;
}

echo "<h2>4. Teste de Carregamento Manual</h2>";
try {
    // Definir constantes necessárias para o CodeIgniter
    if (!defined('BASEPATH')) {
        define('BASEPATH', __DIR__ . '/lib/');
    }
    
    // Incluir o arquivo do modelo
    include_once 'app/models/Sumup_model.php';
    
    echo "<p>✅ Arquivo incluído com sucesso</p>";
    
    // Verificar se a classe existe
    if (class_exists('Sumup_model')) {
        echo "<p>✅ Classe Sumup_model encontrada</p>";
        
        // Tentar instanciar (pode dar erro por causa do CodeIgniter)
        try {
            // Simular o ambiente do CodeIgniter
            $CI = new stdClass();
            $CI->config = new stdClass();
            $CI->load = new stdClass();
            
            // Não vamos instanciar, apenas verificar métodos
            $reflection = new ReflectionClass('Sumup_model');
            $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
            
            echo "<p>✅ Métodos públicos encontrados:</p>";
            echo "<ul>";
            foreach ($methods as $method) {
                if (!$method->isConstructor() && !$method->isDestructor()) {
                    echo "<li>" . $method->getName() . "</li>";
                }
            }
            echo "</ul>";
            
        } catch (Exception $e) {
            echo "<p>⚠️ Erro ao analisar classe (normal): " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>❌ Classe Sumup_model NÃO encontrada</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Erro ao incluir arquivo: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>5. Teste de Carregamento via CodeIgniter</h2>";

// Simular uma requisição ao controlador POS
echo "<p>Testando carregamento via CodeIgniter...</p>";

// Fazer uma requisição HTTP para testar
$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/pos';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ Erro cURL: " . $error . "</p>";
} elseif ($http_code == 200) {
    echo "<p>✅ Controlador POS carregou com sucesso (HTTP 200)</p>";
    
    // Verificar se há erros relacionados ao SumUp na resposta
    if (strpos($response, 'sumup') !== false || strpos($response, 'SumUp') !== false) {
        echo "<p>✅ SumUp mencionado na resposta</p>";
    } else {
        echo "<p>⚠️ SumUp não mencionado na resposta</p>";
    }
    
    // Verificar se há erros PHP na resposta
    if (strpos($response, 'Fatal error') !== false || strpos($response, 'Parse error') !== false) {
        echo "<p>❌ Erro PHP detectado na resposta:</p>";
        $lines = explode("\n", $response);
        foreach ($lines as $line) {
            if (strpos($line, 'error') !== false || strpos($line, 'Error') !== false) {
                echo "<p style='color: red;'>" . htmlspecialchars($line) . "</p>";
            }
        }
    } else {
        echo "<p>✅ Nenhum erro PHP detectado</p>";
    }
    
} else {
    echo "<p>❌ Erro HTTP: " . $http_code . "</p>";
}

echo "<h2>6. Próximos Passos</h2>";
echo "<ol>";
echo "<li>Se todos os testes passaram, o problema pode estar na chamada do método SumUp</li>";
echo "<li>Verifique se o JavaScript está chamando o endpoint correto</li>";
echo "<li>Teste o endpoint create_sumup_checkout diretamente</li>";
echo "</ol>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
