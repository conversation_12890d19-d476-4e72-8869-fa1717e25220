<?php
// Teste rápido da API SumUp
defined('BASEPATH') OR define('BASEPATH', __DIR__);
require_once 'config.php';

// Simular ambiente CodeIgniter básico
if (!function_exists('log_message')) {
    function log_message($level, $message) {
        error_log("[$level] $message");
    }
}

require_once 'app/models/Sumup_model.php';

header('Content-Type: application/json');

try {
    $sumup = new Sumup_model();
    
    if (!$sumup->isConfigured()) {
        echo json_encode([
            'error' => true,
            'message' => 'SumUp não configurado'
        ]);
        exit;
    }
    
    // Criar checkout de teste
    $unique_ref = 'TESTE_QUICK_' . date('YmdHis') . '_' . uniqid();
    $result = $sumup->createCheckout(50.00, 'BRL', 'Teste Rápido', $unique_ref);
    
    if ($result['success']) {
        $hosted_url = $result['data']['hosted_checkout_url'] ?? null;
        
        echo json_encode([
            'success' => true,
            'message' => 'Checkout criado com sucesso!',
            'checkout_id' => $result['data']['id'],
            'hosted_checkout_url' => $hosted_url,
            'internal_url' => 'http://localhost:8010/sistema/sumup/process/' . $result['data']['id'],
            'correct_url' => $hosted_url ? 'SumUp URL' : 'Internal URL',
            'data' => $result['data']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao criar checkout',
            'error' => $result
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'message' => 'Exceção: ' . $e->getMessage()
    ]);
}
?>
