<?php
/**
 * Teste SumUp com Login
 * 
 * Este script faz login primeiro e depois testa o SumUp
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

echo "<h1>🔐 Teste SumUp com Login</h1>";

// Configurações de teste
$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/index.php/';
$username = 'admin'; // Ajuste conforme necessário
$password = 'admin'; // Ajuste conforme necessário

echo "<h2>1. Tentativa de Login</h2>";

// Inicializar sessão cURL para manter cookies
$cookie_jar = tempnam(sys_get_temp_dir(), 'cookies');

// Primeiro, obter a página de login para pegar tokens CSRF se necessário
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . 'login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_jar);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_jar);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$login_page = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    echo "<p>✅ Página de login acessada (HTTP 200)</p>";
    
    // Tentar extrair token CSRF se existir
    $csrf_token = '';
    if (preg_match('/name="spos_token".*?value="([^"]+)"/', $login_page, $matches)) {
        $csrf_token = $matches[1];
        echo "<p>✅ Token CSRF encontrado: " . substr($csrf_token, 0, 10) . "...</p>";
    } else {
        echo "<p>⚠️ Token CSRF não encontrado</p>";
    }
    
} else {
    echo "<p>❌ Erro ao acessar página de login: HTTP {$http_code}</p>";
}

echo "<h2>2. Fazendo Login</h2>";

// Fazer login
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $base_url . 'login');
curl_setopt($ch, CURLOPT_POST, true);

$login_data = [
    'username' => $username,
    'password' => $password
];

if (!empty($csrf_token)) {
    $login_data['spos_token'] = $csrf_token;
}

curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($login_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_jar);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_jar);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$login_response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
echo "<p><strong>URL Final:</strong> {$final_url}</p>";

if (strpos($final_url, 'dashboard') !== false || strpos($final_url, 'pos') !== false) {
    echo "<p>✅ Login realizado com sucesso!</p>";
    $login_success = true;
} else {
    echo "<p>❌ Login falhou - ainda na página de login</p>";
    echo "<p>Verifique as credenciais: username='{$username}', password='{$password}'</p>";
    $login_success = false;
}

if ($login_success) {
    echo "<h2>3. Testando SumUp com Usuário Logado</h2>";
    
    // Agora testar o endpoint SumUp com a sessão ativa
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $base_url . 'pos/create_sumup_checkout');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'amount' => '5.00',
        'description' => 'Teste com Login'
    ]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_jar);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_jar);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'X-Requested-With: XMLHttpRequest'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p>❌ Erro cURL: " . $error . "</p>";
    } else {
        echo "<p><strong>HTTP Code:</strong> {$http_code}</p>";
        echo "<p><strong>URL Final:</strong> {$final_url}</p>";
        
        if ($http_code == 200) {
            echo "<p>✅ Requisição bem-sucedida!</p>";
            
            // Tentar decodificar JSON
            $json_data = json_decode($response, true);
            if ($json_data) {
                echo "<p><strong>Resposta JSON:</strong></p>";
                echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT) . "</pre>";
                
                if (isset($json_data['error']) && $json_data['error']) {
                    echo "<p>⚠️ Erro na resposta: " . $json_data['message'] . "</p>";
                } elseif (isset($json_data['success']) && $json_data['success']) {
                    echo "<p>🎉 Sucesso! Checkout SumUp criado!</p>";
                    echo "<p><strong>Checkout ID:</strong> " . $json_data['checkout_id'] . "</p>";
                    echo "<p><strong>URL:</strong> <a href='" . $json_data['checkout_url'] . "' target='_blank'>" . $json_data['checkout_url'] . "</a></p>";
                }
            } else {
                echo "<p>⚠️ Resposta não é JSON válido:</p>";
                echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
            }
        } else {
            echo "<p>❌ Erro HTTP: {$http_code}</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    }
} else {
    echo "<h2>❌ Não foi possível testar SumUp sem login</h2>";
    echo "<p>Ajuste as credenciais de login no script ou faça login manualmente no navegador</p>";
}

// Limpar arquivo de cookies
if (file_exists($cookie_jar)) {
    unlink($cookie_jar);
}

echo "<h2>4. Verificar Logs</h2>";
echo "<p><a href='view_recent_logs.php' target='_blank'>Ver Logs Recentes</a></p>";

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . "</small></p>";
?>
