<?php
/**
 * Visualizador de Logs Recentes
 * 
 * Este script mostra apenas os logs mais recentes
 */

// Verificar se está em ambiente de desenvolvimento
if (!defined('ENVIRONMENT')) {
    define('ENVIRONMENT', 'development');
}

if (ENVIRONMENT !== 'development') {
    die('Este script só pode ser executado em ambiente de desenvolvimento');
}

echo "<h1>📋 Logs Recentes</h1>";

$log_file = 'app/logs/log-' . date('Y-m-d') . '.php';

if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    // Remover a primeira linha (<?php...)
    if (isset($lines[0]) && strpos($lines[0], '<?php') !== false) {
        array_shift($lines);
    }
    
    // Pegar as últimas 30 linhas
    $recent_lines = array_slice($lines, -30);
    
    echo "<h2>Últimas 30 Entradas</h2>";
    echo "<div style='background: #f5f5f5; padding: 10px; font-family: monospace; max-height: 400px; overflow-y: auto;'>";
    
    foreach ($recent_lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Colorir diferentes tipos de log
        $color = '#333';
        if (strpos($line, 'ERROR') !== false) {
            $color = '#d32f2f';
        } elseif (strpos($line, 'DEBUG') !== false) {
            $color = '#1976d2';
        } elseif (strpos($line, 'INFO') !== false) {
            $color = '#388e3c';
        }
        
        // Destacar linhas relacionadas ao SumUp
        $background = '';
        if (stripos($line, 'sumup') !== false) {
            $background = 'background-color: #fff3cd; font-weight: bold;';
        }
        
        echo "<div style='color: {$color}; margin: 2px 0; {$background}'>" . htmlspecialchars($line) . "</div>";
    }
    
    echo "</div>";
    
    // Procurar especificamente por logs do SumUp
    $sumup_lines = [];
    foreach ($lines as $line) {
        if (stripos($line, 'sumup') !== false) {
            $sumup_lines[] = $line;
        }
    }
    
    if (!empty($sumup_lines)) {
        echo "<h2>🎯 Logs Específicos do SumUp</h2>";
        echo "<div style='background: #e8f5e8; padding: 10px; font-family: monospace;'>";
        foreach ($sumup_lines as $line) {
            echo "<div style='color: #2e7d32; margin: 2px 0;'>" . htmlspecialchars(trim($line)) . "</div>";
        }
        echo "</div>";
    } else {
        echo "<h2>⚠️ Nenhum Log do SumUp Encontrado</h2>";
        echo "<p>Isso pode significar:</p>";
        echo "<ul>";
        echo "<li>O modelo SumUp não está sendo chamado</li>";
        echo "<li>Há um erro antes dos logs serem escritos</li>";
        echo "<li>O endpoint não está sendo acessado</li>";
        echo "</ul>";
    }
    
} else {
    echo "<p>❌ Arquivo de log não encontrado: {$log_file}</p>";
}

echo "<hr>";
echo "<p><small>Data: " . date('d/m/Y H:i:s') . " | Arquivo: {$log_file}</small></p>";

// Botão para atualizar
echo "<script>";
echo "setTimeout(function(){ location.reload(); }, 10000);"; // Auto-refresh a cada 10 segundos
echo "</script>";
echo "<p><em>Esta página será atualizada automaticamente a cada 10 segundos</em></p>";
?>
